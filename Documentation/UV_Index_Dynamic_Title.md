# UV 指數動態標題功能實現

## 概述
根據當前天氣來源 (Weather Source) 動態顯示不同的 UV 指數標題，為 CW (Central Weather) 提供特殊的標題顯示。

## 實現內容

### 1. 多語系翻譯添加

為所有支援的語言添加了新的翻譯鍵 `daily_max_uv_index`：

| 語言 | 翻譯 |
|------|------|
| 🇹🇼 繁體中文 (zh-Hant) | 當日最高紫外線指數 |
| 🇺🇸 英文 (en) | Daily Max UV Index |
| 🇬🇧 英文 (en-GB) | Daily Max UV Index |
| 🇦🇺 英文 (en-AU) | Daily Max UV Index |
| 🇯🇵 日文 (ja) | 本日最高紫外線指数 |
| 🇫🇷 法文 (fr) | Indice UV Max Quotidien |
| 🇨🇦 法文 (fr-CA) | Indice UV Max Quotidien |
| 🇩🇪 德文 (de) | Täglicher Max UV-Index |
| 🇪🇸 西班牙文 (es) | Índice UV Máximo Diario |
| 🇮🇹 意大利文 (it) | Indice UV Massimo Giornaliero |
| 🇳🇱 荷蘭文 (nl) | Dagelijkse Max UV-index |
| 🇩🇰 丹麥文 (da) | Daglig Max UV-indeks |
| 🇸🇪 瑞典文 (sv) | Daglig Max UV-index |
| 🇳🇴 挪威文 (nb) | Daglig Maks UV-indeks |
| 🇫🇮 芬蘭文 (fi) | Päivittäinen Max UV-indeksi |

### 2. UVIndexView 功能擴展

#### 新增參數
```swift
struct UVIndexView: View {
    let uvIndex: String
    let uvColor: Color
    let weatherSource: WeatherSource?  // 新增：天氣來源參數
    let isProFeature: Bool
    let onUpgrade: (() -> Void)?
}
```

#### 動態標題邏輯
```swift
private var titleText: String {
    guard let source = weatherSource else {
        return "uv_index".localized
    }
    
    switch source {
    case .CW:
        return "daily_max_uv_index".localized  // CW 專用標題
    case .OW, .GW, .AW:
        return "uv_index".localized           // 其他來源使用原標題
    }
}
```

### 3. WeatherDetailView 整合

#### 參數傳遞
```swift
UVIndexView(
    uvIndex: getSelectedUVIndex(),
    uvColor: getUVIndexColor(),
    weatherSource: viewModel.currentSavedLocation?.effectiveWeatherSource,  // 傳遞天氣來源
    isProFeature: !iapService.isPro,
    onUpgrade: { showingPaywall = true }
)
```

## 功能邏輯

### 天氣來源對應

| 天氣來源 | 代碼 | 標題顯示 |
|----------|------|----------|
| Central Weather Administration | CW | 當日最高紫外線指數 |
| OpenWeather | OW | 紫外線指數 |
| Google Weather | GW | 紫外線指數 |
| Apple Weather | AW | 紫外線指數 |

### 標題選擇邏輯

1. **檢查天氣來源**：從 `viewModel.currentSavedLocation?.effectiveWeatherSource` 獲取
2. **條件判斷**：
   - 如果是 `CW`：顯示 "當日最高紫外線指數"
   - 如果是 `OW`、`GW`、`AW`：顯示 "紫外線指數"
   - 如果天氣來源為 `nil`：預設顯示 "紫外線指數"

### 多語系自動切換

- 標題會根據用戶的語言設定自動切換
- 支援所有專案中已實現的 12 種語言
- 使用 `.localized` 擴展進行本地化

## 技術實現

### 資料流
```
WeatherDetailView
    ↓ (獲取天氣來源)
viewModel.currentSavedLocation?.effectiveWeatherSource
    ↓ (傳遞給)
UVIndexView
    ↓ (計算標題)
titleText (computed property)
    ↓ (顯示)
Text(titleText)
```

### 安全性考量
- 使用 `guard let` 安全解包天氣來源
- 提供預設值處理 `nil` 情況
- 確保所有天氣來源都有對應的處理邏輯

## 測試要點

### 功能測試
1. **CW 來源測試**
   - 切換到台灣位置
   - 確認天氣來源為 CW
   - 驗證標題顯示為 "當日最高紫外線指數"

2. **其他來源測試**
   - 測試 OW、GW、AW 來源
   - 確認標題顯示為 "紫外線指數"

3. **多語系測試**
   - 切換不同語言
   - 確認標題正確本地化

4. **邊界情況測試**
   - 天氣來源為 `nil` 的情況
   - 位置切換時的標題更新

### UI 測試
- 確認標題文字不會造成佈局問題
- 驗證長標題在不同設備上的顯示效果
- 測試標題變化時的動畫效果

## 注意事項

1. **向後兼容性**：如果天氣來源為 `nil`，會顯示預設標題
2. **即時更新**：當用戶切換天氣來源時，標題會立即更新
3. **多語系一致性**：所有語言的翻譯都保持語意一致
4. **效能考量**：使用計算屬性避免不必要的重複計算
