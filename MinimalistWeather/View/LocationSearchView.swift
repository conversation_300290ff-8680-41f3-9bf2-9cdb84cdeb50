//
//  LocationSearchView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch
import WidgetKit

// 無需明確導入Foundation，因為SwiftUI已經導入了它

struct LocationSearchView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: WeatherViewModel
    var scrollToTopAction: () -> Void
    
    // MARK: - State
    @State private var isSearchMode = false
    @State private var searchText = ""
    @State private var searchResults: [GoogleGeocoding.Result] = []
    @State private var savedLocations: [SavedLocation] = []
    @State private var isLoading = false
    @State private var errorMessage: String?
    @FocusState private var isTextFieldFocused: Bool
    @State private var showingUpgradeAlert = false
    @State private var showingPaywall = false
    @State private var showingWeatherSourcePicker = false
    @State private var selectedLocationForWeatherSource: SavedLocation?
    
    // MARK: - IAP 相關
    @ObservedObject private var iapService = IAPService.shared
    
    // MARK: - Services
    private let geocodingService = GoogleGeocodingService()
    private let locationRepository = LocationRepository()
    private let localizationManager = LocalizationManager.shared
    
    // 初始化時檢查savedLocations
    init(viewModel: WeatherViewModel, scrollToTopAction: @escaping () -> Void) {
        self.viewModel = viewModel
        self.scrollToTopAction = scrollToTopAction
    }
    
    // MARK: - 計算屬性
    
    /// 檢查是否可以新增地區
    private var canAddLocation: Bool {
        // 如果已訂閱 Pro，可以無限新增
        if iapService.isPro {
            return true
        }
        
        // 免費使用者：如果沒有任何地區，可以新增一個
        return savedLocations.isEmpty
    }
    
    /// 檢查是否可以點擊地區項目
    private var canSelectLocation: Bool {
        // 如果已訂閱 Pro，可以點擊任何地區
        if iapService.isPro {
            return true
        }
        
        // 免費使用者：只能有一個地區，所以不能切換
        return false
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("location".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 主要內容區域
            VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
                
                if !isSearchMode && savedLocations.isEmpty {
                    // 新增地區按鈕 (初始狀態)
                    addLocationButton
                } else if isSearchMode {
                    // 搜尋模式
                    searchModeContent
                } else {
                    // 已儲存的地區清單
                    savedLocationsContent
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()
            
            // 底部按鈕區域 (搜尋模式時隱藏)
            if !isSearchMode {
                bottomButtons
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
        // .edgesIgnoringSafeArea(.all)
        .onAppear {
            loadSavedLocations()
        }
        .iapUpgradeAlert(isPresented: $showingUpgradeAlert, showingPaywall: $showingPaywall)
        .fullScreenCover(isPresented: $showingPaywall) {
            PaywallView()
        }
        .fullScreenCover(
            isPresented: Binding(
                get: { showingWeatherSourcePicker && selectedLocationForWeatherSource != nil },
                set: { newValue in
                    showingWeatherSourcePicker = newValue
                    if !newValue {
                        selectedLocationForWeatherSource = nil
                    }
                }
            )
        ) {
            // 這裡我們確保 selectedLocationForWeatherSource 不為 nil
            if let location = selectedLocationForWeatherSource {
                WeatherSourcePickerView(
                    selectedSource: Binding(
                        get: { location.effectiveWeatherSource },
                        set: { newSource in
                            updateLocationWeatherSource(location, newSource: newSource)
                        }
                    ),
                    availableSources: WeatherSource.availableSources(for: location.country),
                    onDismiss: {
                        showingWeatherSourcePicker = false
                    },
                    onConfirm: {
                        showingWeatherSourcePicker = false
                    }
                )
                .onAppear {
                    // 調試信息
                    _ = WeatherSource.availableSources(for: location.country)
                    // Logger.debug("=== 天氣來源選擇器調試 ===")
                    // Logger.debug("位置: \(location.name)")
                    // Logger.debug("國家代碼: '\(location.country)'")
                    // Logger.debug("可用來源: \(availableSources.map { $0.displayName })")
                    // Logger.debug("當前來源: \(location.effectiveWeatherSource.displayName)")
                    // Logger.debug("========================")
                }
            }
        }
    }
    
    // MARK: - 新增地區按鈕
    private var addLocationButton: some View {
        Button(action: {
            if canAddLocation {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isSearchMode = true
                    // 延遲一點讓動畫完成後再 focus
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        isTextFieldFocused = true
                    }
                }
            } else {
                // 顯示需要升級的提示
                showUpgradeAlert()
            }
        }) {
            HStack(spacing: CGFloat(0).auto()) {
                AppIconsSymbol.createView(
                    for: AppIcons.add, 
                    fontSize: CGFloat(44).auto(), 
                    // color: HexColor.color("222222")
                    color: canAddLocation ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                )
                
                Text("add_forecasts_city".localized)
                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                    // .foregroundColor(HexColor.color("222222"))
                    .foregroundColor(canAddLocation ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
            }
            .offset(x: CGFloat(-10).auto())
        }
    }
    
    // MARK: - 搜尋模式內容
    private var searchModeContent: some View {
        VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
            
            // 搜尋輸入框
            HStack(spacing: CGFloat(0).auto()) {
                Button(action: performSearch) {
                    AppIconsSymbol.createView(for: AppIcons.search, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                }
                .disabled(searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)

                TextField("search_city_name".localized, text: $searchText)
                    .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                    .focused($isTextFieldFocused)
                    .onSubmit {
                        performSearch()
                    }                
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        cancelSearch()
                    }
                }) {
                    AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(24).auto(), color: HexColor.themed(.primaryText))
                }
            }
            .offset(x: CGFloat(-10).auto())
            
            // 載入指示器
            if isLoading {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("searching".localized)
                        .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))
                }
                .padding(.vertical, CGFloat(8).auto())
            }
            
            // 錯誤訊息
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.accent))
                    .padding(.vertical, CGFloat(8).auto())
            }
            
            // 搜尋結果清單
            if !searchResults.isEmpty {
                searchResultsList
            }
        }
    }
    
    // MARK: - 搜尋結果清單
    private var searchResultsList: some View {
        VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
            ForEach(searchResults) { result in
                searchResultRow(result)
            }
        }
    }
    
    // MARK: - 搜尋結果行
    private func searchResultRow(_ result: GoogleGeocoding.Result) -> some View {
        Button(action: {
            if canAddLocation {
                // 直接加入位置，不使用 radio box 選擇
                addLocationDirectly(result)
            } else {
                // 顯示需要升級的提示
                showUpgradeAlert()
            }
        }) {
            HStack(spacing: CGFloat(0).auto()) {
                AppIconsSymbol.createView(
                    for: AppIcons.add, 
                    fontSize: CGFloat(44).auto(), 
                    color: HexColor.themed(.primaryText)
                )
                
                VStack(alignment: .leading, spacing: CGFloat(1).auto()) {
                    Text(result.extractCityName())
                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                        .lineLimit(1)
                        .offset(y: CGFloat(7).auto()) // 與上面的文字對齊
                    
                    Text(result.formatted_address)
                        .font(.system(size: CGFloat(12).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))
                        .lineLimit(1)
                        .offset(y: CGFloat(10).auto()) // 與上面的文字對齊
                }
                
                Spacer()
            }
            .offset(x: CGFloat(-10).auto())
            .padding(.vertical, CGFloat(2).auto())
        }
    }
    
    // MARK: - 已儲存的地區內容
    private var savedLocationsContent: some View {
        VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
            
            // 新增更多地區按鈕
            Button(action: {
                if canAddLocation {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isSearchMode = true
                        // 延遲一點讓動畫完成後再 focus
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            isTextFieldFocused = true
                        }
                    }
                } else {
                    // 顯示需要升級的提示
                    showUpgradeAlert()
                }
            }) {
                HStack(spacing: CGFloat(0).auto()) {
                    AppIconsSymbol.createView(
                        for: AppIcons.add, 
                        fontSize: CGFloat(44).auto(), 
                        // color: HexColor.color("222222")
                        color: canAddLocation ? HexColor.themed(.primaryText) : HexColor.themed(.disabled)
                    )
                    
                    Text("add_more_cities".localized)
                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        // .foregroundColor(HexColor.color("222222"))
                        .foregroundColor(canAddLocation ? HexColor.themed(.primaryText) : HexColor.themed(.disabled))
                }
                .offset(x: CGFloat(-10).auto())
            }
            
            // 已儲存的地區清單 - 可捲動區域
            ZStack {
                ScrollView(.vertical, showsIndicators: false) {
                    LazyVStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                        ForEach(savedLocations) { location in
                            savedLocationRow(location)
                        }
                    }
                    .padding(.vertical, CGFloat(8).auto()) // 為淡出效果預留空間
                }
                
                // 頂部淡出漸層
                VStack {
                    LinearGradient(
                        gradient: Gradient(colors: [HexColor.themed(.primaryBackground), HexColor.themed(.primaryBackground).opacity(0)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .frame(height: CGFloat(30).auto())
                    Spacer()
                }
                .allowsHitTesting(false)
                
                // 底部淡出漸層
                VStack {
                    Spacer()
                    LinearGradient(
                        gradient: Gradient(colors: [HexColor.themed(.primaryBackground).opacity(0), HexColor.themed(.primaryBackground)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .frame(height: CGFloat(30).auto())
                }
                .allowsHitTesting(false)
            }
            // .frame(maxHeight: CGFloat(300).auto()) // 限制最大高度以確保可捲動
            .frame(maxHeight: .infinity)
        }
    }
    
    // MARK: - 已儲存地區行
    private func savedLocationRow(_ location: SavedLocation) -> some View {
        VStack(alignment: .leading, spacing: CGFloat(4).auto()) {
            HStack(spacing: CGFloat(0).auto()) {
                Button(action: {
                    removeLocation(location)
                }) {
                    AppIconsSymbol.createView(for: AppIcons.clear, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                }
                
                Button(action: {
                    if canSelectLocation {
                        selectLocation(location)
                    } else {
                        // 免費使用者不能切換地區，顯示升級提示
                        showUpgradeAlert()
                    }
                }) {
                    HStack(spacing: CGFloat(1).auto()) {
                        Text(location.name)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                            .lineLimit(1)
                        // Text(location.formattedAddress)
                        //     .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                        //     .foregroundColor(HexColor.themed(.secondaryText))
                        //     .lineLimit(1)
                    }
                }
                
                Spacer()
                
                // 天氣來源選擇按鈕
                Button(action: {
                    // 同步設置位置和顯示狀態
                    selectedLocationForWeatherSource = location
                    showingWeatherSourcePicker = true
                }) {
                    AppIconsSymbol.createView(for: AppIcons.next, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                }
            }
            .offset(x: CGFloat(-10).auto())

            Text(location.formattedAddress)
                .font(.system(size: CGFloat(12).auto(), weight: .regular, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
                .lineLimit(1)
                .offset(y: CGFloat(-10).auto()) // 與上面的文字對齊
                .offset(x: CGFloat(32).auto()) // 與上面的文字對齊
            
            // 顯示當前天氣來源
            Text("weather_source_setting".localized + ": " + location.effectiveWeatherSource.displayName)
                .font(.system(size: CGFloat(12).auto(), weight: .regular, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
                .offset(y: CGFloat(-10).auto()) // 與上面的文字對齊
                .offset(x: CGFloat(32).auto()) // 與上面的文字對齊
        }
        .padding(.vertical, CGFloat(2).auto())
    }
    
    // MARK: - 底部按鈕
    private var bottomButtons: some View {
        HStack(spacing: CGFloat(20).auto()) {
            HStack {
                // 左側文字按鈕
                Button(action: {
                    // 載入第一個地區並返回主頁
                    loadFirstLocationAndReturn()
                }) {
                    Text("close".localized)
                        .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                }

                Spacer()

                // 右側圓形勾選按鈕
                Button(action: {
                    // 載入第一個地區並返回主頁
                    loadFirstLocationAndReturn()
                }) {
                    HStack(spacing: CGFloat(8).auto()) {
                        AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryBackground))
                    }
                    .padding(.horizontal, CGFloat(2).auto())
                    .padding(.vertical, CGFloat(2).auto())
                    .background(
                        Circle()
                            .fill(HexColor.themed(.primaryText))
                    )
                }
            }
        }
    }
    
    // MARK: - 方法
    
    private func loadSavedLocations() {
        savedLocations = locationRepository.getAllSavedLocations()
    }
    
    private func performSearch() {
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        Logger.debug("開始搜尋: \(searchText)")
        isLoading = true
        errorMessage = nil
        
        geocodingService.searchLocations(searchText, language: localizationManager.currentLanguage) { result in
            DispatchQueue.main.async {
                isLoading = false
                
                switch result {
                case .success(let results):
                    Logger.success("搜尋成功，找到 \(results.count) 個結果")
                    searchResults = results
                    if results.isEmpty {
                        errorMessage = "no_locations_matched".localized
                    }
                case .failure(let error):
                    Logger.error("搜尋失敗: \(error.message)")
                    errorMessage = error.message
                    searchResults = []
                }
            }
        }
    }
    
    private func addLocationDirectly(_ result: GoogleGeocoding.Result) {
        let savedLocation = result.toSavedLocation()

        // 保存位置
        locationRepository.addSavedLocation(savedLocation)

        // 只為新添加的位置獲取時區資訊
        locationRepository.ensureTimezone(for: savedLocation) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let updatedLocation):
                    Logger.success("新位置時區獲取成功: \(updatedLocation.name) - \(updatedLocation.timezone ?? "無時區")")

                    // 更新 ViewModel 的位置列表（不觸發批量時區檢查）
                    viewModel.savedLocations = locationRepository.getAllSavedLocations()

                    // 只更新當前選中的位置，不載入天氣數據
                    viewModel.currentSavedLocation = updatedLocation
                    viewModel.currentLocation = updatedLocation.name

                case .failure(let error):
                    Logger.error("新位置時區獲取失敗: \(error)")

                    // 即使時區獲取失敗，仍然更新位置列表和當前位置
                    viewModel.savedLocations = locationRepository.getAllSavedLocations()
                    viewModel.currentSavedLocation = savedLocation
                    viewModel.currentLocation = savedLocation.name
                }

                // 發送位置資料變更通知，確保 UI 正確更新
                NotificationCenter.default.post(name: Notification.Name("ResetTimelineIndex"), object: nil)

                // 觸發 HorizontalLocationPicker 刷新
                // scrollToTopAction()
            }
        }

        withAnimation(.easeInOut(duration: 0.3)) {
            loadSavedLocations()
            cancelSearch()
        }
    }
    
    private func cancelSearch() {
        isSearchMode = false
        searchText = ""
        searchResults = []
        errorMessage = nil
        isLoading = false
        isTextFieldFocused = false
    }
    
    private func removeLocation(_ location: SavedLocation) {
        withAnimation(.easeInOut(duration: 0.3)) {
            locationRepository.removeSavedLocation(location)

            // 直接更新 ViewModel 的位置列表（不觸發 API 請求）
            viewModel.savedLocations = locationRepository.getAllSavedLocations()

            // 如果刪除的是當前選中的位置，需要更新到第一個位置（但不載入天氣數據）
            if viewModel.currentSavedLocation?.id == location.id {
                if let firstLocation = viewModel.savedLocations.first {
                    // 只更新當前選中的位置，不載入天氣數據
                    viewModel.currentSavedLocation = firstLocation
                    viewModel.currentLocation = firstLocation.name
                    // 保存最後查看的位置ID
                    locationRepository.saveLastViewedLocationID(firstLocation.id)
                } else {
                    // 沒有任何位置了，清空當前位置
                    viewModel.clearCurrentLocation()
                }

                // 不發送會觸發天氣刷新的通知，改為直接更新 UI
                // NotificationCenter.default.post(name: Notification.Name("LocationDataChanged"), object: nil)
            }

            // 觸發 HorizontalLocationPicker 刷新
            // scrollToTopAction()

            loadSavedLocations()
        }
    }
    
    // MARK: - 選擇地區
    private func selectLocation(_ location: SavedLocation) {
        Logger.debug("LocationSearchView: 選擇位置 \(location.name)")
        
        // 使用新的 ViewModel 系統來切換位置
        viewModel.useLocation(location)
        
        // 關閉並返回主頁
        dismiss()
        scrollToTopAction()
    }
    
    // MARK: - 顯示升級提示
    private func showUpgradeAlert() {
        showingUpgradeAlert = true
    }
    
    // MARK: - 更新位置天氣來源
    private func updateLocationWeatherSource(_ location: SavedLocation, newSource: WeatherSource) {
        // 更新位置的天氣來源
        locationRepository.updateLocationWeatherSource(location, newSource: newSource)

        // 重新載入儲存的位置列表（不觸發時區檢查）
        loadSavedLocations()

        // 更新 ViewModel 的位置列表（不觸發 API 呼叫）
        viewModel.loadSavedLocations(checkTimezones: false)

        // 檢查是否需要更新 Widget 數據
        checkAndUpdateWidgetData(for: location, newSource: newSource)

        // 不立即重新載入天氣資料，只在下次手動選擇位置時才會使用新的天氣來源
        Logger.debug("已更新位置 \(location.name) 的天氣來源為 \(newSource.displayName)")
        Logger.debug("新的天氣來源將在下次載入該位置時生效")
    }

    // MARK: - 檢查並更新 Widget 數據
    private func checkAndUpdateWidgetData(for location: SavedLocation, newSource: WeatherSource) {
        // 檢查當前 widget 選中的位置是否與更新的位置相同
        guard let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") else {
            Logger.error("無法初始化 App Group UserDefaults")
            return
        }

        let widgetLocationId = groupDefaults.string(forKey: "widget_selected_location_id")

        // 如果 widget 選中的位置與更新的位置相同，則更新 widget 數據
        if let widgetId = widgetLocationId, widgetId == location.id.uuidString {
            Logger.debug("🔧 檢測到 Widget 位置與更新位置相同，更新 Widget 天氣來源數據")

            // 更新 App Group 中的天氣來源
            groupDefaults.set(newSource.rawValue, forKey: "widget_selected_location_weather_source")
            groupDefaults.synchronize()

            // 通知 Widget 重新載入
            WidgetCenter.shared.reloadAllTimelines()

            Logger.debug("🔧 已更新 Widget 天氣來源為: \(newSource.displayName)")
            Logger.debug("🔧 已通知 Widget 重新載入")
        } else {
            Logger.debug("🔧 Widget 位置與更新位置不同，無需更新 Widget 數據")
            if let widgetId = widgetLocationId {
                Logger.debug("🔧 Widget 位置 ID: \(widgetId)")
                Logger.debug("🔧 更新位置 ID: \(location.id.uuidString)")
            } else {
                Logger.debug("🔧 Widget 尚未設定位置")
            }
        }
    }

    // MARK: - 載入第一個地區並返回主頁
    /// 載入第一個儲存地區並返回主頁（類似 selectLocation 的邏輯）
    private func loadFirstLocationAndReturn() {
        let savedLocations = locationRepository.getAllSavedLocations()

        if let firstLocation = savedLocations.first {
            Logger.debug("LocationSearchView: 載入第一個位置並返回主頁 \(firstLocation.name)")

            // 使用 ViewModel 系統來切換位置並載入天氣資料
            viewModel.useLocation(firstLocation)
        } else {
            Logger.debug("LocationSearchView: 沒有儲存的位置")
        }

        // 關閉並返回主頁
        dismiss()
        // scrollToTopAction()
    }
}

// MARK: - 預覽
struct LocationSearchView_Previews: PreviewProvider {
    static var previews: some View {
        LocationSearchView(viewModel: WeatherViewModel(), scrollToTopAction: {})
    }
}

// MARK: - Placeholder ViewModifier
extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content) -> some View {
        
        ZStack(alignment: alignment) {
            self
            placeholder()
                .opacity(shouldShow ? 1 : 0)
                .allowsHitTesting(false)
        }
    }
} 
