import SwiftUI
import AutoInch
import WidgetKit

// MARK: - Pro功能鎖定ViewModifier
struct ProFeatureLockModifier: ViewModifier {
    let isLocked: Bool
    let onUpgrade: () -> Void
    
    func body(content: Content) -> some View {
        content
            .blur(radius: isLocked ? 20 : 0)
            .overlay(
                isLocked ? 
                Button(action: onUpgrade) {
                    AppIconsSymbol.createView(for: AppIcons.lock, fontSize: CGFloat(34).auto(), color: HexColor.themed(.secondaryText))
                        .frame(maxWidth: .infinity, alignment: .center)
                }
                : nil
            )
    }
}

// MARK: - 尚未設定ViewModifier
struct NotSetModifier: ViewModifier {
    let isLocked: Bool
    let onUpgrade: () -> Void
    
    func body(content: Content) -> some View {
        content
            .blur(radius: isLocked ? 20 : 0)
            .overlay(
                isLocked ? 
                Button(action: onUpgrade) {
                    AppIconsSymbol.createView(for: AppIcons.lock, fontSize: CGFloat(34).auto(), color: HexColor.themed(.secondaryText))
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .offset(x: CGFloat(-8).auto())
                }
                : nil
            )
    }
}

// MARK: - View Extension
extension View {
    func proFeatureLock(isLocked: Bool, onUpgrade: @escaping () -> Void) -> some View {
        self.modifier(ProFeatureLockModifier(isLocked: isLocked, onUpgrade: onUpgrade))
    }
}

// MARK: - View Extension
extension View {
    func notSet(isLocked: Bool, onUpgrade: @escaping () -> Void) -> some View {
        self.modifier(NotSetModifier(isLocked: isLocked, onUpgrade: onUpgrade))
    }
}

struct WeatherDetailView: View {
    var animation: Namespace.ID
    @ObservedObject var viewModel: WeatherViewModel
    let selectedTimeIndex: Int
    let onDismiss: () -> Void

    // MARK: - IAP 相關
    @ObservedObject private var iapService = IAPService.shared
    @State private var showingPaywall = false

    // MARK: - 動畫控制
    @State private var animatedProgress: Double = 0.0

    // MARK: - 城市選擇器相關
    @State private var selectedLocationIndex: Int = 0
    @State private var showingWeatherSourcePicker = false
    @State private var selectedLocationForWeatherSource: SavedLocation? = nil

    // MARK: - UI 刷新控制
    @State private var refreshTrigger: UUID = UUID() // 用於強制刷新 UI

    var body: some View {
        GeometryReader { geometry in
            
            ScrollView {
                VStack(alignment: .leading, spacing: CGFloat(30).auto()) {

                    // 頂部
                    VStack(alignment: .center, spacing: CGFloat(0).auto()) {
                        // 城市選擇器或城市名稱
                        ZStack {
                            if viewModel.savedLocations.count > 1 && iapService.isPro {
                                // 多個城市且已訂閱 Pro 時顯示水平選擇器
                                HorizontalLocationPicker(
                                    locations: viewModel.savedLocations,
                                    selectedIndex: $selectedLocationIndex
                                ) { newIndex in
                                    // 切換到選中的城市
                                    if newIndex < viewModel.savedLocations.count {
                                        let selectedLocation = viewModel.savedLocations[newIndex]
                                        viewModel.useLocation(selectedLocation)
                                    }
                                }
                                .frame(height: CGFloat(20).auto())
                                .padding(.top, CGFloat(62).auto())
                                .padding(.bottom, CGFloat(3).auto())
                            } else {
                                // 單個城市或免費使用者顯示城市名稱
                                Text(getSelectedLocationName().uppercased())
                                    .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.primaryText))
                                    .padding(.top, CGFloat(62).auto())
                                    .padding(.bottom, CGFloat(3).auto())
                            }
                        }
                        .onLongPressGesture(minimumDuration: 0.8) {
                            // 長按作為備用方案，直接打開天氣來源選擇器
                            Logger.debug("檢測到長按，直接打開天氣來源選擇器")
                            openWeatherSourcePicker()
                        }

                        // 日期和時間
                        HStack(spacing: CGFloat(5).auto()) {
                            Text(getSelectedDateString())
                                .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.themed(.secondaryText))
                                .padding(.top, CGFloat(8).auto())
                        }
                    }
                    // .background(HexColor.themed(.success))
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.top, CGFloat(26).auto())
                    
                    
                    // 主要溫度和天氣圖標
                    HStack(alignment: .center) {
                        // 溫度顯示
                        HStack(spacing: CGFloat(2).auto()) {
                            Text(getSelectedTemperature())
                                .font(.system(size: CGFloat(54).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryText))
                            Text("°")
                                .font(.system(size: CGFloat(30).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryText))
                                .offset(y: CGFloat(-8).auto())
                        }
                        .matchedGeometryEffect(id: "temperature", in: animation)
                        
                        Spacer()
                        
                        // 天氣圖標
                        AppIconsSymbol.createView(
                            for: AppIconsSymbol.getWeatherIconFromCode(getSelectedWeatherIcon()), 
                            fontSize: CGFloat(200).auto(), 
                            color: HexColor.themed(.separator)
                        )
                        .matchedGeometryEffect(id: "weatherIcon", in: animation)
                    }
                    .padding(.horizontal, CGFloat(50).auto())

                    // 天氣狀況文字
                    Text(getSelectedWeatherCondition())
                        .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .offset(y: CGFloat(-20).auto())
                        .id(refreshTrigger) // 強制刷新
                                      
                    
                    // 天氣指標網格
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: CGFloat(4).auto()), count: getGridColumnCount()), spacing: CGFloat(30).auto()) {
                        // 體感溫度
                        if getSelectedFeelsLike() == "-99" {
                            MaintenanceView()
                        } else {
                            WeatherMetricView(
                                icon: AppIcons.temperature,
                                value: getSelectedFeelsLike(),
                                unit: "°",
                                label: "feels_like".localized,
                                isProFeature: !iapService.isPro,
                                onUpgrade: { showingPaywall = true }
                            )
                        }
                        
                        // 濕度 - 當 API 回應 -99 時顯示故障檢修
                        if getSelectedHumidity() == "-99" {
                            MaintenanceView()
                        } else {
                            WeatherMetricView(
                                icon: AppIcons.humidity,
                                value: getSelectedHumidity(),
                                unit: "%",
                                label: "humidity".localized
                            )
                        }
                        // .background(HexColor.themed(.success))
                        
                        // 降雨機率
                        if getSelectedPrecipitationProbability() == "-99" {
                            MaintenanceView()
                        } else {
                            WeatherMetricView(
                                icon: AppIcons.rainp,
                                value: getSelectedPrecipitationProbability(),
                                unit: "%",
                                label: "precipitation_probability".localized
                            )
                            // .background(HexColor.themed(.success))
                        }
                        
                        // 雲量 - 當 API 回應 -999 時隱藏
                        if getSelectedCloudiness() != "-999" {
                            WeatherMetricView(
                                icon: AppIcons.cloud,
                                value: getSelectedCloudiness(),
                                unit: "%",
                                label: "cloudiness".localized,
                                isProFeature: !iapService.isPro,
                                onUpgrade: { showingPaywall = true }
                            )
                        }


                    }
                    .padding(.horizontal, CGFloat(50).auto())
                    .id(refreshTrigger) // 強制刷新天氣指標網格

                    // 紫外線指數 - 當 API 回應 -999 時隱藏
                    if getSelectedUVIndex() != "-999" {
                        UVIndexView(
                            uvIndex: getSelectedUVIndex(),
                            uvColor: getUVIndexColor(),
                            weatherSource: viewModel.currentSavedLocation?.effectiveWeatherSource,
                            isProFeature: !iapService.isPro,
                            onUpgrade: { showingPaywall = true }
                        )
                        .padding(.horizontal, CGFloat(50).auto())
                        .frame(maxWidth: .infinity, alignment: .center)
                        .id(refreshTrigger) // 強制刷新 UV 指數視圖
                    }

                    // 日出日落信息
                    SunriseSunsetView(
                        sunrise: getSelectedSunrise(),
                        sunset: getSelectedSunset(),
                        timezoneId: viewModel.currentSavedLocation?.timezone
                    )
                    .padding(.horizontal, CGFloat(50).auto())
                    
                    // 指標
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: CGFloat(40).auto()), count: 2), spacing: CGFloat(20).auto()) {
                        // 風速
                        WeatherDetailMetricView(
                            label: "wind_speed".localized,
                            value: getSelectedWindSpeed(),
                            unit: viewModel.measurementSystem.windSpeedUnit,
                            progress: getWindSpeedProgress(),
                            animatedProgress: animatedProgress,
                            isProFeature: !iapService.isPro,
                            onUpgrade: { showingPaywall = true }
                        )

                        // 陣風
                        WeatherDetailMetricView(
                            label: "wind_gust".localized,
                            value: getSelectedWindGust(),
                            unit: viewModel.measurementSystem.windSpeedUnit,
                            progress: getWindGustProgress(),
                            animatedProgress: animatedProgress,
                            isProFeature: !iapService.isPro,
                            onUpgrade: { showingPaywall = true }
                        )

                        // 能見度
                        WeatherDetailMetricView(
                            label: "visibility".localized,
                            value: getSelectedVisibility(),
                            unit: viewModel.measurementSystem.distanceUnit,
                            progress: getVisibilityProgress(),
                            animatedProgress: animatedProgress,
                            isProFeature: !iapService.isPro,
                            onUpgrade: { showingPaywall = true }
                        )

                        // 海平面氣壓
                        WeatherDetailMetricView(
                            label: "sea_pressure".localized,
                            value: getSelectedSeaPressure(),
                            unit: viewModel.measurementSystem.pressureUnit,
                            progress: getSeaPressureProgress(),
                            animatedProgress: animatedProgress,
                            isProFeature: !iapService.isPro,
                            onUpgrade: { showingPaywall = true }
                        )

                        // 地面氣壓 - 當 API 回應 -999 時隱藏
                        if getSelectedGroundPressure() != "-999" {
                            WeatherDetailMetricView(
                                label: "ground_pressure".localized,
                                value: getSelectedGroundPressure(),
                                unit: viewModel.measurementSystem.pressureUnit,
                                progress: getGroundPressureProgress(),
                                animatedProgress: animatedProgress,
                                isProFeature: !iapService.isPro,
                                onUpgrade: { showingPaywall = true }
                            )
                        }

                        // 降雨量
                        WeatherDetailMetricView(
                            label: "rain_volume".localized,
                            value: getSelectedRainVolume(),
                            unit: viewModel.measurementSystem.precipitationUnit,
                            progress: getRainVolumeProgress(),
                            animatedProgress: animatedProgress,
                            isProFeature: !iapService.isPro,
                            onUpgrade: { showingPaywall = true }
                        )

                        // 降雪量
                        WeatherDetailMetricView(
                            label: "snow_volume".localized,
                            value: getSelectedSnowVolume(),
                            unit: viewModel.measurementSystem.precipitationUnit,
                            progress: getSnowVolumeProgress(),
                            animatedProgress: animatedProgress,
                            isProFeature: !iapService.isPro,
                            onUpgrade: { showingPaywall = true }
                        )
                    }
                    .padding(.horizontal, CGFloat(50).auto())
                    .id(refreshTrigger) // 強制刷新詳細指標
                    
                    Spacer(minLength: CGFloat(50).auto())

                    // Divider()
                }
                // .padding(.horizontal, CGFloat(50).auto())
                // .padding(.bottom, CGFloat(50).auto())

                // VStack ( spacing: CGFloat(2).auto()) {
                //     Text("weather_source_setting".localized)
                //         .font(.system(size: CGFloat(12).auto(), weight: .regular))
                //         .foregroundColor(HexColor.themed(.secondaryText))
                //         .multilineTextAlignment(.center)
                    
                //     Text("viewModel.weatherSource.displayName")
                //     // Text(viewModel.weatherSource.displayName)
                //         .font(.system(size: CGFloat(12).auto(), weight: .medium))
                //         .foregroundColor(HexColor.themed(.primaryText))
                //         .multilineTextAlignment(.center)
                // }
                // .padding(.top, CGFloat(10).auto())
            }
        }
        .background(
            HexColor.themed(.primaryBackground)
            .matchedGeometryEffect(id: "background", in: animation)
            .ignoresSafeArea(.all)
        )
        .onTapGesture {
            withAnimation {
                onDismiss()
            }
        }
        .onAppear {
            // 延遲一點時間開始動畫，讓視圖先完成轉場
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                withAnimation(.easeInOut(duration: 0.8)) {
                    animatedProgress = 1.0
                }
            }

            // 初始化選中的城市索引
            updateSelectedLocationIndex()
        }
        .onChange(of: viewModel.currentSavedLocation) { _ in
            // 當當前城市改變時，更新選中的索引
            updateSelectedLocationIndex()
        }
        .onChange(of: viewModel.savedLocations) { _ in
            // 當城市列表改變時，更新選中的索引
            updateSelectedLocationIndex()
        }
        .onChange(of: viewModel.currentWeatherData?.temperature) { newTemperature in
            // 當天氣資料的溫度更新時，強制重新渲染 UI
            Logger.debug("🔄 WeatherDetailView: 檢測到溫度更新: \(newTemperature ?? 0)°")
            // 觸發 UI 強制刷新
            refreshTrigger = UUID()
        }
        .onChange(of: viewModel.currentWeatherData?.condition) { newCondition in
            // 當天氣資料的狀況更新時，強制重新渲染 UI
            Logger.debug("🔄 WeatherDetailView: 檢測到天氣狀況更新: \(newCondition ?? "未知")")
            // 觸發 UI 強制刷新
            refreshTrigger = UUID()
        }
        .onChange(of: viewModel.timelinePreviewData.count) { newCount in
            // 當時間軸預覽資料數量更新時，強制重新渲染 UI
            Logger.debug("🔄 WeatherDetailView: 檢測到時間軸資料更新，資料筆數: \(newCount)")
            // 觸發 UI 強制刷新
            refreshTrigger = UUID()
        }
        .fullScreenCover(isPresented: $showingPaywall) {
            PaywallView()
        }
        .fullScreenCover(
            isPresented: Binding(
                get: { showingWeatherSourcePicker && selectedLocationForWeatherSource != nil },
                set: { newValue in
                    showingWeatherSourcePicker = newValue
                    if !newValue {
                        selectedLocationForWeatherSource = nil
                    }
                }
            )
        ) {
            // 由於 Binding 已確保 selectedLocationForWeatherSource 不為 nil，這裡可以安全地強制解包
            let location = selectedLocationForWeatherSource!

            WeatherSourcePickerView(
                selectedSource: Binding(
                    get: { location.effectiveWeatherSource },
                    set: { newSource in
                        updateLocationWeatherSource(location, newSource: newSource)
                    }
                ),
                availableSources: WeatherSource.availableSources(for: location.country),
                onDismiss: {
                    showingWeatherSourcePicker = false
                },
                onConfirm: {
                    showingWeatherSourcePicker = false
                }
            )
            .onAppear {
                let availableSources = WeatherSource.availableSources(for: location.country)
                Logger.debug("=== WeatherSourcePickerView onAppear (fullScreenCover) ===")
                Logger.debug("位置: \(location.name)")
                Logger.debug("國家代碼: '\(location.country)'")
                Logger.debug("可用來源數量: \(availableSources.count)")
                Logger.debug("可用來源: \(availableSources.map { $0.displayName })")
                Logger.debug("當前來源: \(location.effectiveWeatherSource.displayName)")
                Logger.debug("====================================")
            }
        }
    }
    
    // MARK: - 數據獲取方法
    
    /// 獲取選中時間點的天氣圖標
    private func getSelectedWeatherIcon() -> String {
        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            return viewModel.timelinePreviewData[selectedTimeIndex].iconCode
        }
        return viewModel.weatherIconName
    }

    /// 獲取選中時間點的溫度
    private func getSelectedTemperature() -> String {
        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            let tempString = viewModel.timelinePreviewData[selectedTimeIndex].temperature
            let numberString = tempString.replacingOccurrences(of: "°C", with: "")
                                       .replacingOccurrences(of: "°F", with: "")
                                       .trimmingCharacters(in: .whitespaces)
            return numberString
        }
        return viewModel.temperatureNumber
    }
    
    /// 獲取選中時間點的天氣狀況
    private func getSelectedWeatherCondition() -> String {
        if selectedTimeIndex == 0 {
            // Index 0 直接使用 API 回應中的天氣描述
            return viewModel.currentWeatherData?.condition ?? viewModel.weatherCondition
        } else {
            // 對於其他時間點，直接從 hourlyForecast 獲取 API 的天氣描述
            if let weather = viewModel.currentWeatherData,
               selectedTimeIndex < weather.hourlyForecast.count {
                return weather.hourlyForecast[selectedTimeIndex].condition
            } else {
                return viewModel.currentWeatherData?.condition ?? viewModel.weatherCondition
            }
        }
    }
    
    /// 獲取選中時間點的日期字符串
    private func getSelectedDateString() -> String {
        let dateTimeFormatter = DateTimeFormatter.shared
        let timezoneId = viewModel.currentSavedLocation?.timezone
        
        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            let date = viewModel.timelinePreviewData[selectedTimeIndex].date
            let dateString = dateTimeFormatter.formatDateWithTimezone(date, timezoneId: timezoneId)
            let timeString = dateTimeFormatter.formatTimeWithTimezone(date, timezoneId: timezoneId, showNow: selectedTimeIndex == 0)
            return "\(dateString) \(timeString)"
            // return "\(dateString)"
        }
        
        let currentDate = Date()
        let dateString = dateTimeFormatter.formatDateWithTimezone(currentDate, timezoneId: timezoneId)
        let timeString = dateTimeFormatter.formatTimeWithTimezone(currentDate, timezoneId: timezoneId)
        return "\(dateString) \(timeString)"
        // return "\(dateString)"
    }
    
    /// 獲取選中位置名稱
    private func getSelectedLocationName() -> String {
        return viewModel.displayLocation
    }
    
    /// 獲取選中時間點的體感溫度
    private func getSelectedFeelsLike() -> String {
        let feelsLikeValue: Int

        if selectedTimeIndex == 0 {
            feelsLikeValue = viewModel.currentWeatherData?.feelsLike ?? 0
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            feelsLikeValue = weather.hourlyForecast[selectedTimeIndex].feelsLike ?? 0
        } else {
            feelsLikeValue = viewModel.currentWeatherData?.feelsLike ?? 0
        }

        // 檢查是否為 -99，如果是則返回特殊標記
        if feelsLikeValue == -99 {
            return "-99"
        }

        return "\(feelsLikeValue)"
    }
    
    /// 獲取選中時間點的濕度
    private func getSelectedHumidity() -> String {
        let humidityValue: Int

        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            let humidityString = viewModel.timelinePreviewData[selectedTimeIndex].humidity.replacingOccurrences(of: "%", with: "")
            humidityValue = Int(humidityString) ?? 0
        } else {
            humidityValue = viewModel.currentWeatherData?.humidity ?? 0
        }

        // 檢查是否為 -99，如果是則返回特殊標記
        if humidityValue == -99 {
            return "-99"
        }

        return "\(humidityValue)"
    }
    
    /// 獲取選中時間點的降雨機率
    private func getSelectedPrecipitationProbability() -> String {
        let precipitationValue: Int

        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            let precipitationString = viewModel.timelinePreviewData[selectedTimeIndex].precipitationProbability.replacingOccurrences(of: "%", with: "")
            precipitationValue = Int(precipitationString) ?? 0
        } else {
            precipitationValue = viewModel.currentWeatherData?.precipitationProbability ?? 0
        }

        // 檢查是否為 -99，如果是則返回特殊標記
        if precipitationValue == -99 {
            return "-99"
        }

        return "\(precipitationValue)"
    }
    
    /// 獲取選中時間點的雲量
    private func getSelectedCloudiness() -> String {
        if selectedTimeIndex == 0 {
            return "\(viewModel.currentWeatherData?.cloudiness ?? 0)"
        }
        if let weather = viewModel.currentWeatherData,
           selectedTimeIndex < weather.hourlyForecast.count {
            return "\(weather.hourlyForecast[selectedTimeIndex].cloudiness ?? 0)"
        }
        return "\(viewModel.currentWeatherData?.cloudiness ?? 0)"
    }
    
    /// 獲取選中時間點的風速
    private func getSelectedWindSpeed() -> String {
        let windSpeedMPS: Double
        if selectedTimeIndex == 0 {
            windSpeedMPS = viewModel.currentWeatherData?.windSpeed ?? 0.0
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            windSpeedMPS = weather.hourlyForecast[selectedTimeIndex].windSpeed ?? 0.0
        } else {
            windSpeedMPS = viewModel.currentWeatherData?.windSpeed ?? 0.0
        }
        
        let convertedSpeed = viewModel.measurementSystem.convertWindSpeed(fromMeterPerSecond: windSpeedMPS)
        return String(format: "%.1f", convertedSpeed)
    }
    
    /// 獲取選中時間點的陣風
    private func getSelectedWindGust() -> String {
        let windGustMPS: Double
        if selectedTimeIndex == 0 {
            windGustMPS = viewModel.currentWeatherData?.windGust ?? 0.0
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            windGustMPS = weather.hourlyForecast[selectedTimeIndex].windGust ?? 0.0
        } else {
            windGustMPS = viewModel.currentWeatherData?.windGust ?? 0.0
        }
        
        let convertedGust = viewModel.measurementSystem.convertWindSpeed(fromMeterPerSecond: windGustMPS)
        return String(format: "%.1f", convertedGust)
    }
    
    /// 獲取選中時間點的能見度
    private func getSelectedVisibility() -> String {
        let visibilityKm: Double
        if selectedTimeIndex == 0 {
            visibilityKm = viewModel.currentWeatherData?.visibility ?? 0.0
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            visibilityKm = weather.hourlyForecast[selectedTimeIndex].visibility ?? 0.0
        } else {
            visibilityKm = viewModel.currentWeatherData?.visibility ?? 0.0
        }
        
        let convertedVisibility = viewModel.measurementSystem.convertDistance(fromKilometers: visibilityKm)
        return String(format: "%.0f", convertedVisibility)
    }
    
    /// 獲取選中時間點的海平面氣壓
    private func getSelectedSeaPressure() -> String {
        let pressureHPa: Double
        if selectedTimeIndex == 0 {
            pressureHPa = viewModel.currentWeatherData?.seaLevelPressure ?? 0.0
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            pressureHPa = weather.hourlyForecast[selectedTimeIndex].seaLevelPressure ?? 0.0
        } else {
            pressureHPa = viewModel.currentWeatherData?.seaLevelPressure ?? 0.0
        }
        
        let convertedPressure = viewModel.measurementSystem.convertPressure(fromHectoPascals: pressureHPa)
        
        switch viewModel.measurementSystem {
        case .metric:
            return String(format: "%.0f", convertedPressure) // 公制顯示整數
        case .imperial:
            return String(format: "%.2f", convertedPressure) // 英制顯示小數
        }
    }
    
    /// 獲取選中時間點的地面氣壓
    private func getSelectedGroundPressure() -> String {
        let pressureHPa: Double
        if selectedTimeIndex == 0 {
            pressureHPa = viewModel.currentWeatherData?.groundLevelPressure ?? 0.0
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            pressureHPa = weather.hourlyForecast[selectedTimeIndex].groundLevelPressure ?? 0.0
        } else {
            pressureHPa = viewModel.currentWeatherData?.groundLevelPressure ?? 0.0
        }

        // 檢查是否為 -999，如果是則直接回傳
        if pressureHPa == -999 {
            return "-999"
        }

        let convertedPressure = viewModel.measurementSystem.convertPressure(fromHectoPascals: pressureHPa)

        switch viewModel.measurementSystem {
        case .metric:
            return String(format: "%.0f", convertedPressure) // 公制顯示整數
        case .imperial:
            return String(format: "%.2f", convertedPressure) // 英制顯示小數
        }
    }
    
    /// 獲取選中時間點的降雨量
    private func getSelectedRainVolume() -> String {
        let rainMM: Double
        if selectedTimeIndex == 0 {
            rainMM = viewModel.currentWeatherData?.rainVolume ?? 0.0
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            rainMM = weather.hourlyForecast[selectedTimeIndex].rainVolume ?? 0.0
        } else {
            rainMM = viewModel.currentWeatherData?.rainVolume ?? 0.0
        }
        
        let convertedRain = viewModel.measurementSystem.convertPrecipitation(fromMillimeters: rainMM)
        return String(format: "%.2f", convertedRain)
    }
    
    /// 獲取選中時間點的降雪量
    private func getSelectedSnowVolume() -> String {
        let snowMM: Double
        if selectedTimeIndex == 0 {
            snowMM = viewModel.currentWeatherData?.snowVolume ?? 0.0
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            snowMM = weather.hourlyForecast[selectedTimeIndex].snowVolume ?? 0.0
        } else {
            snowMM = viewModel.currentWeatherData?.snowVolume ?? 0.0
        }
        
        let convertedSnow = viewModel.measurementSystem.convertPrecipitation(fromMillimeters: snowMM)
        return String(format: "%.2f", convertedSnow)
    }
    
    /// 獲取選中時間點的日出時間
    private func getSelectedSunrise() -> Date? {
        return viewModel.currentWeatherData?.sunrise
    }

    /// 獲取選中時間點的日落時間
    private func getSelectedSunset() -> Date? {
        return viewModel.currentWeatherData?.sunset
    }

    /// 獲取選中時間點的紫外線指數
    private func getSelectedUVIndex() -> String {
        let uvIndex: Int?
        if selectedTimeIndex == 0 {
            uvIndex = viewModel.currentWeatherData?.uvIndex
        } else if let weather = viewModel.currentWeatherData,
                  selectedTimeIndex < weather.hourlyForecast.count {
            uvIndex = weather.hourlyForecast[selectedTimeIndex].uvIndex
        } else {
            uvIndex = viewModel.currentWeatherData?.uvIndex
        }

        // 如果是 -999 或 nil，返回 "-999" 以便隱藏
        guard let uv = uvIndex, uv != -999 else {
            return "-999"
        }

        return String(uv)
    }

    /// 根據 UV 指數獲取對應的顏色
    private func getUVIndexColor() -> Color {
        guard let uvString = getSelectedUVIndex() != "-999" ? getSelectedUVIndex() : nil,
              let uvIndex = Int(uvString) else {
            return HexColor.themed(.uvOne) // 預設顏色
        }

        switch uvIndex {
        case 0...2:
            return HexColor.themed(.uvOne)   // 低 (綠色)
        case 3...5:
            return HexColor.themed(.uvTwo)   // 中等 (黃綠色)
        case 6...7:
            return HexColor.themed(.uvThree) // 高 (橙色)
        case 8...11:
            return HexColor.themed(.uvFour)  // 極高 (紅色)
        default:
            return HexColor.themed(.uvOne)   // 預設顏色
        }
    }

    /// 計算網格列數（根據可顯示的項目數量）
    private func getGridColumnCount() -> Int {
        var count = 3 // 基本項目：體感溫度、濕度、降雨機率

        // 如果雲量不是 -999，增加一列
        if getSelectedCloudiness() != "-999" {
            count += 1
        }

        // 最多 4 列，最少 3 列
        return min(max(count, 3), 4)
    }

    // MARK: - 城市選擇器相關方法

    /// 更新選中的城市索引
    private func updateSelectedLocationIndex() {
        if let currentLocation = viewModel.currentSavedLocation,
           let index = viewModel.savedLocations.firstIndex(where: { $0.id == currentLocation.id }) {
            selectedLocationIndex = index
        }
    }

    /// 打開天氣來源選擇器
    private func openWeatherSourcePicker() {
        guard let currentLocation = viewModel.currentSavedLocation else {
            Logger.error("無法打開天氣來源選擇器：currentSavedLocation 為 nil")
            return
        }

        Logger.debug("=== 打開天氣來源選擇器 ===")
        Logger.debug("當前位置: \(currentLocation.name)")
        Logger.debug("位置ID: \(currentLocation.id)")
        Logger.debug("國家代碼: '\(currentLocation.country)'")
        Logger.debug("當前天氣來源: \(currentLocation.effectiveWeatherSource.displayName)")

        let availableSources = WeatherSource.availableSources(for: currentLocation.country)
        Logger.debug("可用來源數量: \(availableSources.count)")
        Logger.debug("可用來源: \(availableSources.map { $0.displayName })")
        Logger.debug("============================")

        // 直接設置數據並打開 sheet
        selectedLocationForWeatherSource = currentLocation
        showingWeatherSourcePicker = true
    }

    /// 更新位置的天氣來源
    private func updateLocationWeatherSource(_ location: SavedLocation, newSource: WeatherSource) {
        let locationRepository = LocationRepository()

        Logger.debug("=== 更新位置天氣來源 ===")
        Logger.debug("位置: \(location.name)")
        Logger.debug("舊來源: \(location.effectiveWeatherSource.displayName)")
        Logger.debug("新來源: \(newSource.displayName)")
        Logger.debug("========================")

        // 更新位置的天氣來源
        locationRepository.updateLocationWeatherSource(location, newSource: newSource)

        // 重新載入 ViewModel 的位置列表（不觸發 API 呼叫）
        viewModel.loadSavedLocations(checkTimezones: false)

        // 更新 selectedLocationForWeatherSource 以反映最新狀態
        if let updatedLocation = viewModel.savedLocations.first(where: { $0.id == location.id }) {
            selectedLocationForWeatherSource = updatedLocation

            // 如果更新的是當前選中的位置，重新載入天氣資料
            if let currentLocation = viewModel.currentSavedLocation,
               currentLocation.id == location.id {
                // 使用更新後的位置重新載入天氣資料
                viewModel.useLocation(updatedLocation)
                Logger.debug("已更新當前位置 \(updatedLocation.name) 的天氣來源為 \(newSource.displayName)，重新載入天氣資料")
            } else {
                Logger.debug("已更新位置 \(updatedLocation.name) 的天氣來源為 \(newSource.displayName)")
            }
        }

        // 檢查並更新 Widget 數據
        checkAndUpdateWidgetData(for: location, newSource: newSource)
    }

    /// 檢查並更新 Widget 數據
    private func checkAndUpdateWidgetData(for location: SavedLocation, newSource: WeatherSource) {
        guard let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") else {
            Logger.error("無法存取 App Group UserDefaults")
            return
        }

        let widgetLocationId = groupDefaults.string(forKey: "widget_selected_location_id")

        // 如果 widget 選中的位置與更新的位置相同，則更新 widget 數據
        if let widgetId = widgetLocationId, widgetId == location.id.uuidString {
            Logger.debug("🔧 檢測到 Widget 位置與更新位置相同，更新 Widget 天氣來源數據")

            // 更新 App Group 中的天氣來源
            groupDefaults.set(newSource.rawValue, forKey: "widget_selected_location_weather_source")
            groupDefaults.synchronize()

            // 通知 Widget 重新載入
            WidgetCenter.shared.reloadAllTimelines()

            Logger.debug("🔧 已更新 Widget 天氣來源為: \(newSource.displayName)")
            Logger.debug("🔧 已通知 Widget 重新載入")
        } else {
            Logger.debug("🔧 Widget 位置與更新位置不同，無需更新 Widget 數據")
        }
    }
    
    // MARK: - 進度條計算方法
    
    private func getWindSpeedProgress() -> Double {
        let windSpeed = Double(getSelectedWindSpeed()) ?? 0.0
        let maxWindSpeed: Double
        
        switch viewModel.measurementSystem {
        case .metric:
            maxWindSpeed = 25.0 // 最大風速 25 m/s (約90 km/h)
        case .imperial:
            maxWindSpeed = 55.0 // 最大風速 55 mph (約25 m/s)
        }
        
        return min(windSpeed / maxWindSpeed, 1.0)
    }
    
    private func getWindGustProgress() -> Double {
        let windGust = Double(getSelectedWindGust()) ?? 0.0
        let maxWindGust: Double
        
        switch viewModel.measurementSystem {
        case .metric:
            maxWindGust = 35.0 // 最大陣風 35 m/s (約126 km/h)
        case .imperial:
            maxWindGust = 78.0 // 最大陣風 78 mph (約35 m/s)
        }
        
        return min(windGust / maxWindGust, 1.0)
    }
    
    private func getVisibilityProgress() -> Double {
        let visibility = Double(getSelectedVisibility()) ?? 0.0
        let maxVisibility: Double
        
        switch viewModel.measurementSystem {
        case .metric:
            maxVisibility = 10.0 // API文件規定最大能見度 10 km
        case .imperial:
            maxVisibility = 6.2 // 10 km = 6.2 miles
        }
        
        return min(visibility / maxVisibility, 1.0)
    }
    
    private func getSeaPressureProgress() -> Double {
        let pressure = Double(getSelectedSeaPressure()) ?? 1013.25
        let minPressure: Double
        let maxPressure: Double
        
        switch viewModel.measurementSystem {
        case .metric:
            minPressure = 980.0  // 最低氣壓 980 hPa
            maxPressure = 1040.0 // 最高氣壓 1040 hPa
        case .imperial:
            minPressure = 28.94  // 980 hPa = 28.94 inHg
            maxPressure = 30.71  // 1040 hPa = 30.71 inHg
        }
        
        return min(max((pressure - minPressure) / (maxPressure - minPressure), 0.0), 1.0)
    }
    
    private func getGroundPressureProgress() -> Double {
        let pressure = Double(getSelectedGroundPressure()) ?? 999
        let minPressure: Double
        let maxPressure: Double
        
        switch viewModel.measurementSystem {
        case .metric:
            minPressure = 580.0  // 最低氣壓 980 hPa
            maxPressure = 1300.0 // 最高氣壓 1040 hPa
        case .imperial:
            minPressure = 17.13  // 980 hPa = 28.94 inHg
            maxPressure = 38.39  // 1040 hPa = 30.71 inHg
        }
        
        return min(max((pressure - minPressure) / (maxPressure - minPressure), 0.0), 1.0)
    }
    
    private func getRainVolumeProgress() -> Double {
        let rain = Double(getSelectedRainVolume()) ?? 0.0
        let maxRain: Double
        
        switch viewModel.measurementSystem {
        case .metric:
            maxRain = 50.0 // 最大降雨量 50 mm
        case .imperial:
            maxRain = 2.0  // 50 mm = 約 2 inches
        }
        
        return min(rain / maxRain, 1.0)
    }
    
    private func getSnowVolumeProgress() -> Double {
        let snow = Double(getSelectedSnowVolume()) ?? 0.0
        let maxSnow: Double
        
        switch viewModel.measurementSystem {
        case .metric:
            maxSnow = 50.0 // 最大降雪量 50 mm
        case .imperial:
            maxSnow = 2.0  // 50 mm = 約 2 inches
        }
        
        return min(snow / maxSnow, 1.0)
    }
}

// MARK: - 天氣指標視圖組件

struct WeatherMetricView: View {
    let icon: String
    let value: String
    let unit: String
    let label: String
    let isProFeature: Bool
    let onUpgrade: (() -> Void)?
    
    @State private var displayValue: String = "0"
    
    init(icon: String, value: String, unit: String, label: String, isProFeature: Bool = false, onUpgrade: (() -> Void)? = nil) {
        self.icon = icon
        self.value = value
        self.unit = unit
        self.label = label
        self.isProFeature = isProFeature
        self.onUpgrade = onUpgrade
    }
    
    var body: some View {
        VStack(spacing: CGFloat(2).auto()) {
            AppIconsSymbol.createView(for: icon, fontSize: CGFloat(40).auto(), color: HexColor.themed(.primaryText))
            
            HStack(spacing: CGFloat(1).auto()) {
                Text(unit)
                    .font(.system(size: CGFloat(8).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
                    .opacity(0) // 隱藏第一個單位，用於對齊
                Text(displayValue)
                    .font(.system(size: CGFloat(18).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
                Text(unit)
                    .font(.system(size: CGFloat(8).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
            }
            .proFeatureLock(isLocked: isProFeature) {
                onUpgrade?()
            }
            
            Text(label)
                .font(.system(size: CGFloat(10).auto(), weight: .regular))
                .foregroundColor(HexColor.themed(.secondaryText))
                .multilineTextAlignment(.center)
        }
        .onAppear {
            startCountUpAnimation()
        }
    }
    
    private func startCountUpAnimation() {
        guard let targetValue = Double(value) else { 
            displayValue = value
            return 
        }
        
        // 延遲一點時間開始數字動畫
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            let duration: Double = 0.5
            let steps = 60
            let increment = targetValue / Double(steps)
            let interval = duration / Double(steps)
            var current = 0.0
            
            Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { timer in
                if current >= targetValue {
                    timer.invalidate()
                    displayValue = value
                } else {
                    current += increment
                    displayValue = String(format: "%.0f", current)
                }
            }
        }
    }
}

struct WeatherDetailMetricView: View {
    let label: String
    let value: String
    let unit: String
    let progress: Double
    let animatedProgress: Double
    let isProFeature: Bool
    let onUpgrade: (() -> Void)?
    
    @State private var displayValue: String = "0"
    
    init(label: String, value: String, unit: String, progress: Double, animatedProgress: Double, isProFeature: Bool = false, onUpgrade: (() -> Void)? = nil) {
        self.label = label
        self.value = value
        self.unit = unit
        self.progress = progress
        self.animatedProgress = animatedProgress
        self.isProFeature = isProFeature
        self.onUpgrade = onUpgrade
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
            Text(label)
                .font(.system(size: CGFloat(12).auto(), weight: .regular))
                .foregroundColor(HexColor.themed(.secondaryText))
            
            HStack(spacing: CGFloat(5).auto()) {
                Text(displayValue)
                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                Text(unit)
                    .font(.system(size: CGFloat(12).auto(), weight: .regular))
                    .foregroundColor(HexColor.themed(.secondaryText))
            }
            .notSet(isLocked: isProFeature) {
                onUpgrade?()
            }
            
            // 進度條
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(HexColor.themed(.progressBar))
                        .frame(height: CGFloat(2).auto())
                    
                    Rectangle()
                        .fill(HexColor.themed(.primaryText))
                        .frame(width: geometry.size.width * progress * animatedProgress, height: CGFloat(2).auto())
                }
            }
            .frame(height: CGFloat(2).auto())
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .onAppear {
            startCountUpAnimation()
        }
        .onChange(of: animatedProgress) { _ in
            if animatedProgress > 0 {
                startCountUpAnimation()
            }
        }
    }
    
    private func startCountUpAnimation() {
        guard let targetValue = Double(value) else { return }
        
        // 延遲一點時間開始數字動畫，與進度條同步
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            let duration: Double = 0.8
            let steps = 60
            let increment = targetValue / Double(steps)
            let interval = duration / Double(steps)
            var current = 0.0
            
            Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { timer in
                if current >= targetValue {
                    timer.invalidate()
                    displayValue = formatValue(targetValue)
                } else {
                    current += increment
                    displayValue = formatValue(current)
                }
            }
        }
    }
    
    private func formatValue(_ val: Double) -> String {
        // 根據原始值的格式來決定顯示格式
        if value.contains(".") {
            // 如果原始值包含小數點，保持小數格式
            let decimalPlaces = value.components(separatedBy: ".")[1].count
            return String(format: "%.\(decimalPlaces)f", val)
        } else {
            // 如果原始值是整數，顯示為整數
            return String(format: "%.0f", val)
        }
    }
}

// MARK: - 日出日落視圖組件

struct SunriseSunsetView: View {
    let sunrise: Date?
    let sunset: Date?
    let timezoneId: String?
    
    @State private var animatedProgress: Double = 0.0
    
    var body: some View {
        VStack(spacing: CGFloat(0).auto()) {
            // Text("SUNSET & SUNRISE")
            //     .font(.system(size: CGFloat(14).auto(), weight: .medium))
            //     .foregroundColor(HexColor.themed(.secondaryText))
            //     .tracking(1.5)
            
            if let sunrise = sunrise, let sunset = sunset {
                VStack(spacing: CGFloat(0).auto()) {                   
                    // 太陽軌跡弧線
                    SunPathArcView(
                        sunrise: sunrise,
                        sunset: sunset,
                        animatedProgress: animatedProgress
                    )
                    .frame(height: CGFloat(120).auto())
                    // .background(HexColor.color("222222"))

                    // 日出日落時間顯示
                    HStack {
                        VStack(spacing: CGFloat(8).auto()) {
                            Text("sunrise".localized)
                                .font(.system(size: CGFloat(12).auto(), weight: .regular))
                                .foregroundColor(HexColor.themed(.secondaryText))
                            
                            Text(DateTimeFormatter.shared.formatSunTimeWithTimezone(sunrise, timezoneId: timezoneId))
                                .font(.system(size: CGFloat(20).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryText))
                        }
                        
                        Spacer()
                        
                        VStack(spacing: CGFloat(8).auto()) {
                            Text("sunset".localized)
                                .font(.system(size: CGFloat(12).auto(), weight: .regular))
                                .foregroundColor(HexColor.themed(.secondaryText))
                            
                            Text(DateTimeFormatter.shared.formatSunTimeWithTimezone(sunset, timezoneId: timezoneId))
                                .font(.system(size: CGFloat(20).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryText))
                        }
                    }
                    .padding(.horizontal, CGFloat(20).auto())
                }
            } else {
                Text("--:--")
                    .font(.system(size: CGFloat(32).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
            }
        }
        // .padding(.horizontal, CGFloat(20).auto())
        .padding(.bottom, CGFloat(20).auto())
        // .background(HexColor.color("222222"))
        // .background(
        //     RoundedRectangle(cornerRadius: CGFloat(16).auto())
        //         .fill(HexColor.themed(.secondaryBackground).opacity(0.3))
        // )
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.easeInOut(duration: 1.0)) {
                    animatedProgress = 1.0
                }
            }
        }
    }
}

// MARK: - 太陽軌跡弧線視圖

struct SunPathArcView: View {
    let sunrise: Date
    let sunset: Date
    let animatedProgress: Double
    
    var body: some View {
        GeometryReader { geometry in
            let width = geometry.size.width
            let height = geometry.size.height
            let centerX = width / 2
            let baselineY = height * 0.8
            let peakY = height * 0.2
            
            ZStack {
                // 地平線
                Rectangle()
                    .fill(HexColor.themed(.progressBar))
                    .frame(maxWidth: .infinity)
                    .frame(height: 1)
                    .position(x: centerX, y: baselineY)

                // 背景虛線弧線（完整的駝峰形狀）
                createHumpPath(width: width, height: height, baselineY: baselineY, peakY: peakY)
                    .stroke(
                        HexColor.themed(.separator).opacity(0.4),
                        style: StrokeStyle(lineWidth: 2, lineCap: .round, dash: [6, 4])
                    )
                
                // 主要弧線（白天部分 - 駝峰樣式）
                createHumpPath(width: width, height: height, baselineY: baselineY, peakY: peakY)
                    .trim(from: 0, to: getSunProgressForArc() * animatedProgress)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.orange.opacity(0.9),
                                Color.yellow.opacity(0.9),
                                Color.orange.opacity(0.9)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        style: StrokeStyle(lineWidth: 5, lineCap: .round)
                    )
                
                
                // 日出圖標
                // AppIconsSymbol.createView(
                //     for: AppIcons.sunrise,
                //     fontSize: CGFloat(18).auto(),
                //     color: Color.orange.opacity(0.8)
                // )
                // .position(x: width * 0.1, y: baselineY - CGFloat(8).auto())
                // .opacity(animatedProgress)
                
                // 日落圖標
                // AppIconsSymbol.createView(
                //     for: AppIcons.sunset,
                //     fontSize: CGFloat(18).auto(),
                //     color: Color.orange.opacity(0.8)
                // )
                // .position(x: width * 0.9, y: baselineY - CGFloat(8).auto())
                // .opacity(animatedProgress)
                
                // 當前太陽位置（如果在白天時間範圍內）
                if let sunPosition = getCurrentSunPosition(width: width, height: height, baselineY: baselineY, peakY: peakY) {
                    ZStack {
                        // 太陽光暈效果
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [Color.yellow.opacity(0.3), Color.clear]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 12
                                )
                            )
                            .frame(width: CGFloat(24).auto(), height: CGFloat(24).auto())
                        
                        // 太陽主體
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [Color.yellow, Color.orange]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 6
                                )
                            )
                            .frame(width: CGFloat(12).auto(), height: CGFloat(12).auto())
                    }
                    .position(sunPosition)
                    // .scaleEffect(animatedProgress)
                    // .animation(.spring(response: 0.8, dampingFraction: 0.6), value: animatedProgress)
                }
            }
        }
    }
    
    // 創建駝峰形狀的路徑（類似 Apple 天氣應用）
    private func createHumpPath(width: CGFloat, height: CGFloat, baselineY: CGFloat, peakY: CGFloat) -> Path {
        Path { path in
            let startX = width * 0.1
            let endX = width * 0.9
            _ = width * 0.5
            
            // 使用更自然的控制點來創建駝峰
            let controlPoint1X = width * 0.25
            let controlPoint2X = width * 0.75
            let controlPoint1Y = peakY + (baselineY - peakY) * 0.1  // 更接近峰頂
            let controlPoint2Y = peakY + (baselineY - peakY) * 0.1  // 更接近峰頂
            
            // 起始點（日出位置）
            path.move(to: CGPoint(x: startX, y: baselineY))
            
            // 使用三次貝塞爾曲線創建平滑的駝峰
            path.addCurve(
                to: CGPoint(x: endX, y: baselineY),
                control1: CGPoint(x: controlPoint1X, y: controlPoint1Y),
                control2: CGPoint(x: controlPoint2X, y: controlPoint2Y)
            )
        }
    }
    
    private func getCurrentSunPosition(width: CGFloat, height: CGFloat, baselineY: CGFloat, peakY: CGFloat) -> CGPoint? {
        let now = Date()
        
        // 直接使用 UTC 時間，不做時區轉換
        // 檢查是否在白天時間範圍內
        guard now >= sunrise && now <= sunset else {
            return nil
        }
        
        // 計算太陽在駝峰曲線上的位置
        let totalDaylight = sunset.timeIntervalSince(sunrise)
        let currentProgress = now.timeIntervalSince(sunrise) / totalDaylight
        
        // 根據駝峰曲線計算位置
        let startX = width * 0.1
        let endX = width * 0.9
        let controlPoint1X = width * 0.25
        let controlPoint2X = width * 0.75
        let controlPoint1Y = peakY + (baselineY - peakY) * 0.1
        let controlPoint2Y = peakY + (baselineY - peakY) * 0.1
        
        // 使用貝塞爾曲線公式計算當前位置
        let t = currentProgress
        let x = pow(1 - t, 3) * startX + 
                3 * pow(1 - t, 2) * t * controlPoint1X + 
                3 * (1 - t) * pow(t, 2) * controlPoint2X + 
                pow(t, 3) * endX
        
        let y = pow(1 - t, 3) * baselineY + 
                3 * pow(1 - t, 2) * t * controlPoint1Y + 
                3 * (1 - t) * pow(t, 2) * controlPoint2Y + 
                pow(t, 3) * baselineY
        
        return CGPoint(x: x, y: y)
    }
    
    // 獲取太陽在弧線上的當前進度（用於弧線動畫）
    private func getSunProgressForArc() -> Double {
        let now = Date()
        
        // 直接使用 UTC 時間，不做時區轉換
        // 檢查是否在白天時間範圍內
        guard now >= sunrise && now <= sunset else {
            return 0.0  // 如果不在白天，弧線進度為0
        }
        
        // 計算太陽在白天時間中的進度
        let totalDaylight = sunset.timeIntervalSince(sunrise)
        let currentProgress = now.timeIntervalSince(sunrise) / totalDaylight
        
        return max(0, min(1, currentProgress))
    }
}

// MARK: - UV Index View

struct UVIndexView: View {
    let uvIndex: String
    let uvColor: Color
    let weatherSource: WeatherSource?
    let isProFeature: Bool
    let onUpgrade: (() -> Void)?

    @State private var displayValue: String = "0"

    /// 根據天氣來源獲取標題
    private var titleText: String {
        guard let source = weatherSource else {
            return "uv_index".localized
        }

        switch source {
        case .CW:
            return "daily_max_uv_index".localized
        case .OW, .GW, .AW:
            return "uv_index".localized
        }
    }

    var body: some View {
        VStack(spacing: CGFloat(0).auto()) {
            // UV 指數內容
            VStack(spacing: CGFloat(0).auto()) {
                // UV 圖標
                AppIconsSymbol.createView(
                    for: AppIcons.uv,
                    fontSize: CGFloat(40).auto(),
                    color: HexColor.themed(.primaryText)
                )

                // 分隔線
                Rectangle()
                    .fill(uvColor)
                    .frame(width: CGFloat(88).auto(), height: CGFloat(2).auto())
                    .padding(.top, CGFloat(2).auto())
                    .padding(.bottom, CGFloat(8).auto())

                // UV 指數數值
                Text(displayValue)
                    .font(.system(size: CGFloat(18).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
                // 標題
                Text(titleText)
                    .font(.system(size: CGFloat(12).auto(), weight: .regular))
                    .foregroundColor(HexColor.themed(.secondaryText))
            }
            .proFeatureLock(isLocked: isProFeature) {
                onUpgrade?()
            }
        }
        .onAppear {
            // 數值動畫
            withAnimation(.easeInOut(duration: 0.8)) {
                displayValue = uvIndex
            }
        }
        .onChange(of: uvIndex) { newValue in
            withAnimation(.easeInOut(duration: 0.3)) {
                displayValue = newValue
            }
        }
    }
}

// MARK: - 濕度故障檢修視圖組件

struct MaintenanceView: View {
    var body: some View {
        VStack(spacing: CGFloat(2).auto()) {
            AppIconsSymbol.createView(for: AppIcons.humidity, fontSize: CGFloat(40).auto(), color: HexColor.themed(.primaryText))

            // 顯示故障檢修文字，不顯示單位
            Text("-")
                .font(.system(size: CGFloat(18).auto(), weight: .regular, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
                .multilineTextAlignment(.center)

            Text("maintenance_status".localized)
                .font(.system(size: CGFloat(10).auto(), weight: .regular))
                .foregroundColor(HexColor.themed(.secondaryText))
                .multilineTextAlignment(.center)
        }
    }
}
