//
//  WidgetLocationPickerView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/13.
//

import SwiftUI
import AutoInch
import WidgetKit

// MARK: - 小工具位置選擇器模態框
struct WidgetLocationPickerView: View {
    @ObservedObject var viewModel: WeatherViewModel
    let onDismiss: () -> Void

    @State private var selectedLocationId: UUID?
    @State private var showingUpgradeAlert = false
    @State private var showingPaywall = false

    // MARK: - IAP 相關
    @ObservedObject private var iapService = IAPService.shared

    // 使用 @AppStorage 來共享數據
    @AppStorage("widget_selected_location_name", store: UserDefaults(suiteName: "group.com.minlsm.weather"))
    private var widgetLocationName = ""

    @AppStorage("widget_selected_location_id", store: UserDefaults(suiteName: "group.com.minlsm.weather"))
    private var widgetLocationId = ""

    // MARK: - 計算屬性

    /// 檢查是否可以選擇位置（需要 Pro 訂閱）
    private var canSelectLocation: Bool {
        return iapService.isPro
    }

    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("widget_settings".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 主要內容區域
            VStack(alignment: .leading, spacing: CGFloat(12).auto()) {                

                VStack(alignment: .leading, spacing: CGFloat(8).auto()) {                    
                    // 已儲存的地區清單 - 可捲動區域
                    ZStack {
                        ScrollView(.vertical, showsIndicators: false) {
                            LazyVStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                                // 如果沒有儲存的位置，顯示提示
                                if viewModel.savedLocations.isEmpty {
                                    Text("請先在位置管理中添加位置")
                                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                        .foregroundColor(HexColor.themed(.secondaryText))
                                        .padding(.vertical, CGFloat(20).auto())
                                } else {
                                    // 顯示儲存的位置清單
                                    ForEach(viewModel.savedLocations) { location in
                                        Button(action: {
                                            if canSelectLocation {
                                                // 有 Pro 訂閱，可以選擇位置
                                                selectLocation(location)
                                            } else {
                                                // 沒有 Pro 訂閱，顯示升級提示
                                                showUpgradeAlert()
                                            }
                                        }) {
                                            HStack(spacing: CGFloat(0).auto()) {
                                                // Radio button
                                                AppIconsSymbol.createView(
                                                    for: selectedLocationId == location.id ? AppIcons.radioboxcheck : AppIcons.radiobox,
                                                    fontSize: CGFloat(44).auto(),
                                                    color: selectedLocationId == location.id ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                                                )
                                                .opacity(canSelectLocation ? (selectedLocationId == location.id ? 1.0 : 0.3) : 0.2)

                                                VStack(alignment: .leading, spacing: CGFloat(2).auto()) {
                                                    HStack {
                                                        Text(location.name)
                                                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                                            .foregroundColor(selectedLocationId == location.id ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                                                            .opacity(canSelectLocation ? (selectedLocationId == location.id ? 1.0 : 0.3) : 0.4)
                                                            .offset(y: CGFloat(7).auto()) // 與上面的文字對齊

                                                        // 如果沒有 Pro 訂閱，顯示 Pro 標籤
                                                        // if !canSelectLocation {
                                                        //     Text("PRO")
                                                        //         .font(.system(size: CGFloat(10).auto(), weight: .bold, design: .rounded))
                                                        //         .foregroundColor(.white)
                                                        //         .padding(.horizontal, CGFloat(6).auto())
                                                        //         .padding(.vertical, CGFloat(2).auto())
                                                        //         .background(
                                                        //             RoundedRectangle(cornerRadius: CGFloat(4).auto())
                                                        //                 .fill(Color.orange)
                                                        //         )
                                                        // }
                                                    }

                                                    Text(location.formattedAddress)
                                                        .font(.system(size: CGFloat(12).auto(), weight: .light, design: .rounded))
                                                        .foregroundColor(HexColor.themed(.secondaryText))
                                                        .lineLimit(1)
                                                        .opacity(canSelectLocation ? (selectedLocationId == location.id ? 0.8 : 0.3) : 0.3)
                                                        .offset(y: CGFloat(10).auto()) // 與上面的文字對齊
                                                }

                                                Spacer()
                                            }
                                            .offset(x: CGFloat(-10).auto())
                                        }
                                        .padding(.vertical, CGFloat(4).auto())
                                    }
                                }
                            }
                            .padding(.vertical, CGFloat(8).auto()) // 為淡出效果預留空間
                        }
                        
                        // 頂部淡出漸層
                        VStack {
                            LinearGradient(
                                gradient: Gradient(colors: [HexColor.themed(.primaryBackground), HexColor.themed(.primaryBackground).opacity(0)]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                            .frame(height: CGFloat(30).auto())
                            Spacer()
                        }
                        .allowsHitTesting(false)
                        
                        // 底部淡出漸層
                        VStack {
                            Spacer()
                            LinearGradient(
                                gradient: Gradient(colors: [HexColor.themed(.primaryBackground).opacity(0), HexColor.themed(.primaryBackground)]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                            .frame(height: CGFloat(30).auto())
                        }
                        .allowsHitTesting(false)
                    }
                    // .frame(maxHeight: CGFloat(300).auto()) // 限制最大高度以確保可捲動
                    .frame(maxHeight: .infinity)
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()
            
            // 底部按鈕區域
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        onDismiss()
                    }) {
                        Text("close".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                    
                    // 右側圓形勾選按鈕
                    Button(action: {
                        onDismiss()
                    }) {
                        HStack(spacing: CGFloat(8).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryBackground))
                        }
                        .padding(.horizontal, CGFloat(2).auto())
                        .padding(.vertical, CGFloat(2).auto())
                        .background(
                            Circle()
                                .fill(HexColor.themed(.primaryText))
                        )
                    }
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
        .iapUpgradeAlert(isPresented: $showingUpgradeAlert, showingPaywall: $showingPaywall)
        .fullScreenCover(isPresented: $showingPaywall) {
            PaywallView()
        }
        .onAppear {
            // 載入當前選擇的位置
            selectedLocationId = WidgetSettingsManager.shared.getSelectedLocationId()
        }
        .onChange(of: iapService.isPro) { isPro in
            // 當 IAP 狀態變化時，同步 UI 狀態
            if !isPro {
                // 降級到免費版，清除 UI 選擇狀態
                selectedLocationId = nil
                // 清除 @AppStorage 數據以同步 UI
                widgetLocationName = ""
                widgetLocationId = ""
                Logger.debug("🔧 UI 狀態已同步：清除 Widget 位置選擇")
            }
        }

    }

    // MARK: - 私有方法

    /// 選擇位置（僅在有 Pro 訂閱時調用）
    private func selectLocation(_ location: SavedLocation) {
        selectedLocationId = location.id

        // 使用 @AppStorage 儲存數據
        widgetLocationName = location.name
        widgetLocationId = location.id.uuidString

        // 儲存完整的位置信息到 App Group
        if let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") {
            groupDefaults.set(location.name, forKey: "widget_selected_location_name")
            groupDefaults.set(location.id.uuidString, forKey: "widget_selected_location_id")
            groupDefaults.set(location.lat, forKey: "widget_selected_location_lat")
            groupDefaults.set(location.lon, forKey: "widget_selected_location_lon")
            groupDefaults.set(location.country, forKey: "widget_selected_location_country")
            if let timezone = location.timezone {
                groupDefaults.set(timezone, forKey: "widget_selected_location_timezone")
            } else {
                groupDefaults.removeObject(forKey: "widget_selected_location_timezone")
            }
            groupDefaults.set(location.effectiveWeatherSource.rawValue, forKey: "widget_selected_location_weather_source")
            groupDefaults.set(location.formattedAddress, forKey: "widget_selected_location_address")
            groupDefaults.synchronize()

            Logger.debug("🔧 完整位置信息已儲存到 App Group:")
            Logger.debug("🔧 Name: \(location.name)")
            Logger.debug("🔧 ID: \(location.id.uuidString)")
            Logger.debug("🔧 Lat: \(location.lat)")
            Logger.debug("🔧 Lon: \(location.lon)")
            Logger.debug("🔧 Country: \(location.country)")
            Logger.debug("🔧 Timezone: \(location.timezone ?? "nil")")
            Logger.debug("🔧 Weather Source: \(location.effectiveWeatherSource.rawValue)")
        }

        // 同時使用舊方法確保相容性
        WidgetSettingsManager.shared.setSelectedLocation(location)

        // 通知 Widget 更新
        WidgetCenter.shared.reloadAllTimelines()

        Logger.debug("🔧 選擇小工具位置: \(location.name)")
        Logger.debug("🔧 @AppStorage 已更新: \(widgetLocationName)")
    }

    /// 顯示升級提示
    private func showUpgradeAlert() {
        showingUpgradeAlert = true
    }


}

// MARK: - 預覽
#Preview {
    WidgetLocationPickerView(
        viewModel: WeatherViewModel(),
        onDismiss: {}
    )
}
