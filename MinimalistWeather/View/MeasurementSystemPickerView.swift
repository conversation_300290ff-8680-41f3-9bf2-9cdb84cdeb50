//
//  MeasurementSystemPickerView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct MeasurementSystemPickerView: View {
    @Binding var selectedSystem: MeasurementSystem
    let onDismiss: () -> Void
    let onConfirm: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("measurement_system_setting".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 主要內容區域
            VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                
                // 公制選擇按鈕
                Button(action: {
                    selectedSystem = .metric
                    Logger.debug("選擇公制")
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: selectedSystem == .metric ? AppIcons.radioboxcheck : AppIcons.radiobox,
                            fontSize: CGFloat(44).auto(),
                            color: selectedSystem == .metric ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(selectedSystem == .metric ? 1.0 : 0.3)
                        
                        Text("metric_system".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(selectedSystem == .metric ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(selectedSystem == .metric ? 1.0 : 0.3)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
                
                // 英制選擇按鈕
                Button(action: {
                    selectedSystem = .imperial
                    Logger.debug("選擇英制")
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: selectedSystem == .imperial ? AppIcons.radioboxcheck : AppIcons.radiobox,
                            fontSize: CGFloat(44).auto(),
                            color: selectedSystem == .imperial ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(selectedSystem == .imperial ? 1.0 : 0.3)
                        
                        Text("imperial_system".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(selectedSystem == .imperial ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(selectedSystem == .imperial ? 1.0 : 0.3)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()
            
            // 底部按鈕區域
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        onConfirm()
                    }) {
                        Text("close".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                    
                    // 右側圓形勾選按鈕
                    Button(action: {
                        onConfirm()
                    }) {
                        HStack(spacing: CGFloat(8).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryBackground))
                        }
                        .padding(.horizontal, CGFloat(2).auto())
                        .padding(.vertical, CGFloat(2).auto())
                        .background(
                            Circle()
                                .fill(HexColor.themed(.primaryText))
                        )
                    }
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
    }
}

#Preview {
    MeasurementSystemPickerView(
        selectedSystem: .constant(.metric),
        onDismiss: {},
        onConfirm: {}
    )
} 