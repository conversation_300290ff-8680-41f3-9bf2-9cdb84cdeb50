/* 
  Localizable.strings (German)
  MinimalistWeather
*/

// MARK: - Onboarding
"welcome" = "Willkommen";
"terms_privacy" = "Bedingungen & Datenschutz";
"privacy_policy" = "Datenschutzrichtlinie";
"terms_of_service" = "Nutzungsbedingungen";
"accept" = "Akzeptieren";
"location" = "Standorte";
"add_forecasts_city" = "Standort hinzufügen";
"searching" = "Sucht...";
"confirm_add" = "Hinzufügen bestätigen";
"add_more_cities" = "Weitere Standorte";
"choose_your_plan" = "Wähle deinen Plan";
"feature_plans_available" = "Funktionspläne werden hier verfügbar sein";
"coming_soon" = "Bald verfügbar...";
"continue" = "Weiter";
"all_set" = "Alles klar!";
"ready_to_start" = "Du kannst Minimalist Weather jetzt nutzen";
"get_started" = "Los geht's";

// MARK: - Main App
"no_connection" = "KEINE VERBINDUNG";
"check_internet_connection" = "Bitte prüfe deine Internetverbindung";
"feedback" = "Feedback";

// MARK: - Weather
"server_error" = "Zeitüberschreitung";
"send_feedback_check_announcements" = "Versuchen Sie, neu zu laden oder den Anbieter zu wechseln.";

// MARK: - Settings
"unit" = "EINHEIT";
"pro" = "Pro-Einstellungen";
"language" = "Sprache";
"time_format" = "Zeitformat";
"12_hour_format" = "12-Stunden-Format";
"24_hour_format" = "24-Stunden-Format";

// MARK: - Location Search
"search_city_name" = "Ort oder Adresse eingeben";
"no_locations_matched" = "KEINE TREFFER";
"search_for_a_city" = "SUCHE EINEN ORT";
"no_saved_locations" = "KEINE GESPEICHERTEN ORTE";
"unlimited_locations_available" = "Unbegrenzte Standorte verfügbar.";
"upgrade_to_pro_add_more" = "Upgrade auf Pro für mehr Standorte.";

// MARK: - Paywall
"thanks_for_pro" = "Danke für dein Premium-Upgrade! Wir freuen uns auf deine Ideen.";
"pro_description" = "Bei Fragen oder Vorschlägen, lass es uns einfach wissen.";
"unlock_full_experience" = "Schalte das volle Minimal-Erlebnis frei";
"5_day_forecast" = "5-Tage-Vorschau";
"multiple_locations" = "Mehrere Standorte";
"all_future_features" = "Alle zukünftigen Funktionen inklusive";
"purchasing" = "Kauf wird verarbeitet...";
"upgrade" = "Upgraden";
"restoring" = "Wiederherstellen...";
"restore" = "Wiederherstellen";
"purchase_agreement" = "Bedingungen | Datenschutz";
"terms" = "Bedingungen";
"loading" = "LADE";

// MARK: - Common
"ok" = "OK";
"cancel" = "Abbrechen";
"search" = "Suchen";
"close" = "Schließen";
"add" = "Hinzufügen";
"delete" = "Löschen";
"edit" = "Bearbeiten";
"retry" = "Neu laden";
"auto_retrying" = "Lädt neu...";
"change_weather_source" = "Anbieter wechseln";

// MARK: - Time
"am" = "AM";
"pm" = "PM";
"now" = "JETZT";

// MARK: - Weekdays
"monday" = "MO";
"tuesday" = "DI";
"wednesday" = "MI";
"thursday" = "DO";
"friday" = "FR";
"saturday" = "SA";
"sunday" = "SO";

// MARK: - Weather Units
"celsius" = "°C";
"fahrenheit" = "°F";
"percent" = "%";

// MARK: - Feature Plans
"current_weather_forecast" = "Aktuelle Wettervorhersage";
"3_hour_2_day_forecast" = "48-Stunden-Vorschau";
"1_location_forecast" = "1 Standort-Vorschau";
"3_hour_5_day_forecast" = "5-Tage-Wettervorhersage";
"2_location_forecast" = "50-Städte-Vorschau";
"detailed_weather_info" = "Detaillierte Wetterdaten";
"custom_night_theme" = "Eigenes Nacht-Thema";
"no_ads" = "Werbefrei";
"start_free" = "Kostenlos starten";
"start_7_day_trial" = "3 Tage kostenlos testen";
"monthly_plan" = "Monatlich";
"yearly_plan" = "Jährlich -50 %";

// MARK: - Alerts
"no_connection_alert" = "Keine Verbindung";
"connect_internet_message" = "Bitte für Wetter-Updates mit dem Internet verbinden.";

// MARK: - IAP Upgrade
"upgrade_to_pro" = "Upgrade auf Pro";
"upgrade_now" = "Jetzt upgraden";
"upgrade_message_multiple_locations" = "Upgrade auf Pro für mehrere Standorte und mehr Funktionen.";

// MARK: - Setup Completion Alert
"please_complete_setup" = "Bitte Einrichtung abschließen";
"complete_setup_before_using" = "Du musst die Einrichtung abschließen, um dies zu nutzen.";
"go_to_setup" = "Zur Einrichtung";

// MARK: - Subscription Period
"per_year" = "/J.";
"per_month" = "/Mon.";

// MARK: - Settings Menu
"about" = "ÜBER";

// MARK: - Sunrise Sunset
"sunrise" = "Sonnenaufgang";
"sunset" = "Sonnenuntergang";
"sunset_sunrise" = "Sonnenauf- & -untergang";

// MARK: - Paywall Continue
"continue_using" = "Weiter nutzen";

// MARK: - Google Geocoding Errors
"network_error" = "Netzwerkfehler";
"invalid_response" = "Ungültige Antwort";
"api_error" = "Bitte versuche einen anderen oder genaueren Ort";
"decoding_error" = "Daten konnten nicht gelesen werden";
"no_search_results" = "Keine Suchergebnisse";

// MARK: - Settings Page Titles
"temperature_unit_setting" = "Temperatureinheit";
"time_format_setting" = "Zeitformat";
"theme_setting" = "Thema-Einstellung";
"weather_source_setting" = "Wetteranbieter";

// MARK: - Theme Settings
"theme_system" = "System";
"theme_light" = "Helles Thema";
"theme_dark" = "Dunkles Thema";

// MARK: - Temperature Units with Symbols
"celsius_with_symbol" = "Celsius °C";
"fahrenheit_with_symbol" = "Fahrenheit °F";

// MARK: - Weather Data Status
"maintenance_status" = "Wartung";

// MARK: - Weather Sources
"apple_weather" = "Apple Weather";
"google_weather" = "Google Weather";
"central_weather_administration" = "Central Weather Administration";

// MARK: - Widget Settings
"widget_settings" = "Widget-Einstellungen";

// MARK: - What's New
"whats_new" = "Was ist neu";
"show_whats_new" = "Neuheiten anzeigen";
"release_notes" = "Versionshinweise";
"version" = "Version";
"no_updates_available" = "Keine Updates verfügbar";

// MARK: - Purchase & Subscription Alerts
"purchase_failed" = "Kauf fehlgeschlagen";
"subscription_success" = "Abo erfolgreich";
"thank_you_for_subscribing" = "Danke für dein Abonnement!";
"error" = "Fehler";
"package_not_available" = "Paket nicht verfügbar";
"cannot_get_user_information" = "Nutzerinformationen können nicht abgerufen werden";
"restore_failed" = "Wiederherstellung fehlgeschlagen";
"restore_success" = "Wiederherstellung erfolgreich";
"purchase_restored" = "Dein Kauf wurde wiederhergestellt";
"no_restorable_items" = "Keine wiederherstellbaren Käufe";
"no_restorable_items_message" = "Wir konnten keine früheren Käufe zum Wiederherstellen finden.";

// MARK: - Paywall Carousel
"paywall_forecasts_120hr_title" = "Volle Vorschau freischalten";
"paywall_forecasts_120hr_subtitle" = "Langzeit-Vorschau & Detaildaten.";
"paywall_saved_50_locations_title" = "50 Standorte speichern";
"paywall_saved_50_locations_subtitle" = "Gratisversion ist auf 1 Standort limitiert.";
"paywall_home_widget_title" = "Home-Widget";
"paywall_home_widget_subtitle" = "In der Gratisversion nicht verfügbar.";
"paywall_night_theme_title" = "Dunkles Thema";
"paywall_night_theme_subtitle" = "Dunkles Thema ist eine Premium-Funktion.";
"paywall_switch_provider_title" = "Anbieter wechseln";
"paywall_switch_provider_subtitle" = "In der Gratisversion nicht verfügbar.";

// Unknown weather
"weather_unknown" = "Unbekanntes Wetter";

// MARK: - Measurement System
"metric_system" = "Metrisch";
"imperial_system" = "Imperial";
"measurement_system_setting" = "Maßsystem";

// MARK: - Wind Speed Units
"wind_speed_ms" = "m/s";
"wind_speed_mph" = "mph";

// MARK: - Distance Units
"distance_km" = "km";
"distance_mi" = "mi";

// MARK: - Precipitation Units
"precipitation_mm" = "mm";
"precipitation_in" = "in";

// MARK: - Pressure Units
"pressure_hpa" = "hPa";
"pressure_inhg" = "inHg";

// MARK: - Weather Detail Labels
"feels_like" = "Gefühlt";
"humidity" = "Luftfeuchte";
"precipitation_probability" = "Niederschlag";
"cloudiness" = "Bewölkung";
"uv_index" = "UV-Index";
"daily_max_uv_index" = "Täglicher Max UV-Index";
"wind_speed" = "Wind";
"wind_gust" = "Böen";
"visibility" = "Sichtweite";
"sea_pressure" = "Meeresdruck";
"ground_pressure" = "Bodendruck";
"rain_volume" = "Regen";
"snow_volume" = "Schnee";

// MARK: - Weather Providers
"weather_providers_info" = "Anbieterinformationen";
"more_providers_info" = "Mehr Anbieterinfos →";
"different_weather_models_info" = "Verschiedene Wettermodelle bieten unterschiedliche Prognosezeiten.";
"weather_provider_apple" = "Apple Wetter";
"weather_provider_apple_subtitle" = "Unterstützt alle Länder\n120-Stunden-Vorschau";
"weather_provider_cwa" = "Central Weather Administration";
"weather_provider_cwa_subtitle" = "Nur für Taiwan\n72-Stunden-Vorschau";
"weather_provider_google" = "Google Wetter";
"weather_provider_google_subtitle" = "Unterstützt Japan oder Korea nicht\n120-Stunden-Vorschau";
"weather_provider_openweather" = "OpenWeather";
"weather_provider_openweather_subtitle" = "Unterstützt alle Länder\n120-Stunden-Vorschau";

// MARK: - Weather Sources
"weather_source_jma" = "Japan Meteorological Agency";
"weather_source_eccc" = "Environment and Climate Change Canada";
"weather_source_dwd" = "Deutscher Wetterdienst";
"weather_source_nws_noaa" = "National Weather Service";
"weather_source_metoffice_ecmwf" = "The Met Office/European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_weather_com" = "Weather";
"weather_source_cwb" = "Weather Forecast Center";
"weather_source_environment_canada" = "Environment Canada";
"weather_source_eumetnet" = "EUMETNET";
"weather_source_ecmwf" = "European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_noaa" = "National Oceanic and Atmospheric Administration (NOAA)";
"weather_source_metoffice" = "Met Office";
"weather_source_gem_cmc" = "GEM (CMC, Canadian Meteorological Centre)";
