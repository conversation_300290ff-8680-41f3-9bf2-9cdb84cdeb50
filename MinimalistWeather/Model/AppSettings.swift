//
//  AppSettings.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation
import WidgetKit

/// 溫度單位枚舉
enum TemperatureUnit: String, Codable, CaseIterable {
    /// 攝氏度
    case celsius
    /// 華氏度
    case fahrenheit
    
    /// 單位顯示名稱
    var displayName: String {
        switch self {
        case .celsius:
            return "celsius_with_symbol".localized
        case .fahrenheit:
            return "fahrenheit_with_symbol".localized
        }
    }
    
    /// 單位符號
    var symbol: String {
        switch self {
        case .celsius:
            return "°C"
        case .fahrenheit:
            return "°F"
        }
    }
    
    /// 溫度轉換 (從攝氏轉換到當前單位)
    /// - Parameter celsius: 攝氏度溫度
    /// - Returns: 轉換後的溫度
    func convert(fromCelsius celsius: Double) -> Double {
        switch self {
        case .celsius:
            return celsius
        case .fahrenheit:
            return celsius * 9 / 5 + 32
        }
    }
    
    /// 將華氏溫度轉換為攝氏溫度
    /// - Parameter fahrenheit: 華氏溫度
    /// - Returns: 攝氏溫度
    func convertToCelsius(fromFahrenheit fahrenheit: Double) -> Double {
        return (fahrenheit - 32) * 5 / 9
    }
}

/// 測量單位系統枚舉
enum MeasurementSystem: String, Codable, CaseIterable {
    /// 公制
    case metric
    /// 英制
    case imperial
    
    /// 單位系統顯示名稱
    var displayName: String {
        switch self {
        case .metric:
            return "metric_system".localized
        case .imperial:
            return "imperial_system".localized
        }
    }
    
    /// 風速單位
    var windSpeedUnit: String {
        switch self {
        case .metric:
            return "wind_speed_ms".localized // "m/s"
        case .imperial:
            return "wind_speed_mph".localized // "mph"
        }
    }
    
    /// 距離單位
    var distanceUnit: String {
        switch self {
        case .metric:
            return "distance_km".localized // "km"
        case .imperial:
            return "distance_mi".localized // "mi"
        }
    }
    
    /// 降雨量單位
    var precipitationUnit: String {
        switch self {
        case .metric:
            return "precipitation_mm".localized // "mm"
        case .imperial:
            return "precipitation_in".localized // "in"
        }
    }
    
    /// 氣壓單位
    var pressureUnit: String {
        switch self {
        case .metric:
            return "pressure_hpa".localized // "hPa"
        case .imperial:
            return "pressure_inhg".localized // "inHg"
        }
    }
    
    /// 風速轉換 (從 m/s 轉換到當前單位)
    /// - Parameter meterPerSecond: 米/秒
    /// - Returns: 轉換後的風速
    func convertWindSpeed(fromMeterPerSecond meterPerSecond: Double) -> Double {
        switch self {
        case .metric:
            return meterPerSecond // 保持 m/s
        case .imperial:
            return meterPerSecond * 2.237 // 轉換為 mph
        }
    }
    
    /// 距離轉換 (從公里轉換到當前單位)
    /// - Parameter kilometers: 公里
    /// - Returns: 轉換後的距離
    func convertDistance(fromKilometers kilometers: Double) -> Double {
        switch self {
        case .metric:
            return kilometers
        case .imperial:
            return kilometers * 0.621371 // 轉換為英里
        }
    }
    
    /// 降雨量轉換 (從毫米轉換到當前單位)
    /// - Parameter millimeters: 毫米
    /// - Returns: 轉換後的降雨量
    func convertPrecipitation(fromMillimeters millimeters: Double) -> Double {
        switch self {
        case .metric:
            return millimeters
        case .imperial:
            return millimeters * 0.0393701 // 轉換為英寸
        }
    }
    
    /// 氣壓轉換 (從 hPa 轉換到當前單位)
    /// - Parameter hectoPascals: 百帕
    /// - Returns: 轉換後的氣壓
    func convertPressure(fromHectoPascals hectoPascals: Double) -> Double {
        switch self {
        case .metric:
            return hectoPascals
        case .imperial:
            return hectoPascals * 0.02953 // 轉換為英寸汞柱
        }
    }
}

/// 時間格式枚舉
enum TimeFormat: String, Codable, CaseIterable {
    /// 12小時制
    case twelveHour
    /// 24小時制
    case twentyFourHour
    
    /// 格式顯示名稱
    var displayName: String {
        switch self {
        case .twelveHour:
            return "12_hour_format".localized
        case .twentyFourHour:
            return "24_hour_format".localized
        }
    }
    
    /// 是否為12小時制
    var is12Hour: Bool {
        return self == .twelveHour
    }
    
    /// 是否為24小時制
    var is24Hour: Bool {
        return self == .twentyFourHour
    }
}

/// 主題模式枚舉
enum ThemeMode: String, Codable, CaseIterable {
    /// 系統偵測
    case system
    /// 白天主題
    case light
    /// 夜晚主題
    case dark
    
    /// 主題顯示名稱
    var displayName: String {
        switch self {
        case .system:
            return "theme_system".localized
        case .light:
            return "theme_light".localized
        case .dark:
            return "theme_dark".localized
        }
    }
}

/// 預報模式枚舉
enum ForecastMode: String, Codable, CaseIterable {
    case hourly = "hourly"
    case daily = "daily"

    /// 預報模式顯示名稱
    var displayName: String {
        switch self {
        case .hourly:
            return "每小時"
        case .daily:
            return "每日"
        }
    }

    /// 是否為每小時模式
    var isHourly: Bool {
        return self == .hourly
    }
}

/// 天氣來源枚舉
enum WeatherSource: String, Codable, CaseIterable {
    /// Apple Weather
    case AW
    /// OpenWeather
    case OW
    /// Google Weather
    case GW
    /// Central Weather (Taiwan)
    case CW
    
    /// 天氣來源顯示名稱
    var displayName: String {
        switch self {
        case .AW:
            return "apple_weather".localized
        case .OW:
            return "OpenWeather"
        case .GW:
            return "google_weather".localized
        case .CW:
            return "central_weather_administration".localized
        }
    }
    
    /// 檢查該來源是否支持指定的國家代碼
    /// - Parameter countryCode: 國家代碼（如 "TW", "JP", "US"）
    /// - Returns: 是否支持該國家
    func isSupported(in countryCode: String) -> Bool {
        switch self {
        case .AW, .OW:
            // AW 和 OW 支援全球
            return true
        case .CW:
            // CW 只支援台灣
            return countryCode == "TW"
        case .GW:
            // GW 不支援日本和韓國
            return !["JP", "KR"].contains(countryCode)
        }
    }
    
    /// 獲取指定國家的可用天氣來源
    /// - Parameter countryCode: 國家代碼
    /// - Returns: 可用的天氣來源陣列
    static func availableSources(for countryCode: String) -> [WeatherSource] {
        return WeatherSource.allCases.filter { $0.isSupported(in: countryCode) }
    }
    
    /// 獲取指定國家的預設天氣來源
    /// - Parameter countryCode: 國家代碼
    /// - Returns: 預設天氣來源
    static func defaultSource(for countryCode: String) -> WeatherSource {
        switch countryCode {
        case "TW":
            return .OW
        case "JP", "KR":
            return .OW
        default:
            return .OW
        }
    }
}

/// 應用設置
class AppSettings {
    /// 單例實例
    static let shared = AppSettings()

    /// UserDefaults 鍵
    private enum Keys {
        static let temperatureUnit = "temperatureUnit"
        static let timeFormat = "timeFormat"
        static let measurementSystem = "measurementSystem"
        static let themeMode = "themeMode"
        static let forecastMode = "forecastMode"
        static let paywallDebugMode = "paywallDebugMode"
    }

    /// 同步當前設定到 App Groups（供 Widget 使用）
    func syncSettingsToAppGroups() {
        guard let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") else {
            Logger.error("無法初始化 App Groups UserDefaults")
            return
        }

        // 同步主題設定
        groupDefaults.set(themeMode.rawValue, forKey: Keys.themeMode)

        // 同步溫度單位設定
        groupDefaults.set(temperatureUnit.rawValue, forKey: Keys.temperatureUnit)

        // 同步時間格式設定
        groupDefaults.set(timeFormat.rawValue, forKey: Keys.timeFormat)

        groupDefaults.synchronize()

        Logger.debug("🔄 設定已同步到 App Groups: 主題=\(themeMode.rawValue), 溫度單位=\(temperatureUnit.rawValue), 時間格式=\(timeFormat.rawValue)")
    }
    
    /// 溫度單位
    var temperatureUnit: TemperatureUnit {
        get {
            guard let rawValue = UserDefaults.standard.string(forKey: Keys.temperatureUnit),
                  let unit = TemperatureUnit(rawValue: rawValue) else {
                // 默認使用系統溫度單位
                return detectSystemTemperatureUnit()
            }
            return unit
        }
        set {
            UserDefaults.standard.set(newValue.rawValue, forKey: Keys.temperatureUnit)

            // 同步到 App Group 供 Widget 使用
            if let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") {
                groupDefaults.set(newValue.rawValue, forKey: Keys.temperatureUnit)
                groupDefaults.synchronize()

                // 通知 Widget 更新
                WidgetCenter.shared.reloadAllTimelines()

                Logger.debug("🔧 溫度單位已同步到 App Group: \(newValue.rawValue)")
            }
        }
    }
    
    /// 時間格式
    var timeFormat: TimeFormat {
        get {
            guard let rawValue = UserDefaults.standard.string(forKey: Keys.timeFormat),
                  let format = TimeFormat(rawValue: rawValue) else {
                // 默認使用系統時間格式
                return detectSystemTimeFormat()
            }
            return format
        }
        set {
            UserDefaults.standard.set(newValue.rawValue, forKey: Keys.timeFormat)

            // 同步到 App Group 供 Widget 使用
            if let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") {
                groupDefaults.set(newValue.rawValue, forKey: Keys.timeFormat)
                groupDefaults.synchronize()

                // 通知 Widget 更新
                WidgetCenter.shared.reloadAllTimelines()

                Logger.debug("🕐 時間格式已同步到 App Group: \(newValue.rawValue)")
            }
        }
    }
    
    /// 測量單位系統
    var measurementSystem: MeasurementSystem {
        get {
            guard let rawValue = UserDefaults.standard.string(forKey: Keys.measurementSystem),
                  let system = MeasurementSystem(rawValue: rawValue) else {
                // 默認使用系統測量單位
                return detectSystemMeasurementSystem()
            }
            return system
        }
        set {
            UserDefaults.standard.set(newValue.rawValue, forKey: Keys.measurementSystem)
        }
    }
    
    /// 主題模式
    var themeMode: ThemeMode {
        get {
            guard let rawValue = UserDefaults.standard.string(forKey: Keys.themeMode),
                  let theme = ThemeMode(rawValue: rawValue) else {
                // 默認使用白天主題
                return .light
            }
            return theme
        }
        set {
            // 儲存到標準 UserDefaults
            UserDefaults.standard.set(newValue.rawValue, forKey: Keys.themeMode)

            // 同時儲存到 App Groups，供 Widget 使用
            if let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") {
                groupDefaults.set(newValue.rawValue, forKey: Keys.themeMode)
                groupDefaults.synchronize()
                Logger.debug("🎨 主題設定已同步到 App Groups: \(newValue.rawValue)")

                // 通知 Widget 重新載入以應用新主題
                WidgetCenter.shared.reloadAllTimelines()
                Logger.debug("🔄 已通知 Widget 重新載入 timeline")
            } else {
                Logger.error("無法同步主題設定到 App Groups")
            }
        }
    }

    /// 預報模式（每小時或每日）
    var forecastMode: ForecastMode {
        get {
            guard let rawValue = UserDefaults.standard.string(forKey: Keys.forecastMode),
                  let mode = ForecastMode(rawValue: rawValue) else {
                // 預設使用每小時預報
                return .hourly
            }
            return mode
        }
        set {
            UserDefaults.standard.set(newValue.rawValue, forKey: Keys.forecastMode)
            Logger.debug("🔧 預報模式已儲存: \(newValue.displayName)")
        }
    }

    /// Paywall Debug 模式（開發者用）
    var paywallDebugMode: Bool {
        get {
            return UserDefaults.standard.bool(forKey: Keys.paywallDebugMode)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: Keys.paywallDebugMode)
            UserDefaults.standard.synchronize()
            Logger.debug("🐛 Paywall Debug 模式已\(newValue ? "啟用" : "關閉")")
        }
    }

    /// 偵測系統時間格式
    private func detectSystemTimeFormat() -> TimeFormat {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        
        let testDate = Date()
        let timeString = formatter.string(from: testDate)
        
        // 檢查是否包含 AM/PM 指示符
        let isSystem12Hour = timeString.contains(formatter.amSymbol) || timeString.contains(formatter.pmSymbol)
        return isSystem12Hour ? .twelveHour : .twentyFourHour
    }
    
    /// 偵測系統溫度單位
    private func detectSystemTemperatureUnit() -> TemperatureUnit {
        let locale = Locale.current
        
        // 檢查地區代碼，美國、利比里亞、緬甸使用華氏溫度
        if let regionCode = locale.regionCode {
            let fahrenheitRegions = ["US", "LR", "MM"]
            if fahrenheitRegions.contains(regionCode) {
                return .fahrenheit
            }
        }
        
        // 其他地區預設使用攝氏溫度
        return .celsius
    }
    
    /// 偵測系統測量單位系統
    private func detectSystemMeasurementSystem() -> MeasurementSystem {
        let locale = Locale.current
        
        // 檢查地區代碼，美國、利比里亞、緬甸使用英制
        if let regionCode = locale.regionCode {
            let imperialRegions = ["US", "LR", "MM"]
            if imperialRegions.contains(regionCode) {
                return .imperial
            }
        }
        
        // 其他地區預設使用公制
        return .metric
    }
    
    /// 私有初始化
    private init() {}
} 