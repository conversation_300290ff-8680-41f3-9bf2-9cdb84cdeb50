//
//  GoogleGeocodingService.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation

// MARK: - Google Geocoding 命名空間
enum GoogleGeocoding {}

/// Google Geocoding API 服務
class GoogleGeocodingService {
    
    // MARK: - 屬性
    private let apiKey: String = {
        guard let apiKey = Bundle.main.object(forInfoDictionaryKey: "GoogleGeocodingAPIKey") as? String, !apiKey.isEmpty else {
            Logger.warning("無法從Info.plist讀取Google Geocoding API key")
            return "YOUR_GOOGLE_GEOCODING_API_KEY"
        }
        Logger.debug("Google Geocoding API Key已載入: \(String(apiKey.prefix(10)))...")
        return apiKey
    }()
    
    private let baseURL = "https://maps.googleapis.com/maps/api/geocode/json"
    
    // 修復iOS 18.4模擬器URLSession bug
    private lazy var urlSession: URLSession = {
        #if targetEnvironment(simulator)
        // 使用ephemeral配置修復模擬器網路問題
        Logger.debug("🔧 使用ephemeral URLSession配置修復模擬器網路問題")
        return URLSession(configuration: .ephemeral)
        #else
        return URLSession.shared
        #endif
    }()
    
    // MARK: - 錯誤類型
    enum GeocodingError: Error {
        case networkError
        case invalidResponse
        case apiError(String)
        case decodingError
        case noResults
        
        var message: String {
            switch self {
            case .networkError:
                return "network_error".localized
            case .invalidResponse:
                return "invalid_response".localized
            // case .apiError(let message):
                // return "\("api_error".localized): \(message)"
            case .apiError(_):
                return "api_error".localized
            case .decodingError:
                return "decoding_error".localized
            case .noResults:
                return "no_search_results".localized
            }
        }
    }
    
    // MARK: - 公開方法
    
    /// 搜尋地點
    /// - Parameters:
    ///   - query: 搜尋關鍵詞
    ///   - language: 語言代碼 (預設: zh-TW)
    ///   - completion: 完成回調
    func searchLocations(_ query: String, language: String = "en-US", completion: @escaping (Swift.Result<[GoogleGeocoding.Result], GeocodingError>) -> Void) {
        
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            completion(.failure(.noResults))
            return
        }
        
        // 確保所有參數都正確編碼
        guard let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let encodedLanguage = language.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) else {
            Logger.error("Google Geocoding API 參數編碼失敗")
            completion(.failure(.invalidResponse))
            return
        }
        
        // 手動構建URL以確保正確編碼
        let urlString = "\(baseURL)?address=\(encodedQuery)&language=\(encodedLanguage)&key=\(apiKey)"
        
        Logger.debug("Google Geocoding API 請求參數:")
        Logger.debug("- 原始 address: \(query)")
        Logger.debug("- 編碼後 address: \(encodedQuery)")
        Logger.debug("- language: \(language)")
        Logger.debug("- key: \(String(apiKey.prefix(10)))...")
        
        guard let url = URL(string: urlString) else {
            Logger.error("Google Geocoding API URL構建失敗: \(urlString)")
            completion(.failure(.invalidResponse))
            return
        }
        
        Logger.debug("Google Geocoding API 請求URL: \(url.absoluteString)")
        
        // 創建URLRequest並設置模擬器修復選項
        var request = URLRequest(url: url)

        // 添加 iOS Bundle Identifier 標頭以符合 API 安全性限制
        if let bundleIdentifier = Bundle.main.bundleIdentifier {
            request.setValue(bundleIdentifier, forHTTPHeaderField: "X-Ios-Bundle-Identifier")
            Logger.debug("Google Geocoding API 設置 Bundle Identifier: \(bundleIdentifier)")
        } else {
            Logger.warning("無法獲取 Bundle Identifier")
        }

        #if targetEnvironment(simulator)
        // 禁用HTTP/3以修復iOS 18.4模擬器問題
        request.assumesHTTP3Capable = false
        Logger.debug("🔧 禁用HTTP/3以修復iOS 18.4模擬器問題")
        #endif
        
        urlSession.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    Logger.error("Google Geocoding API 網路錯誤: \(error.localizedDescription)")
                    
                    // 詳細錯誤診斷
                    if let nsError = error as NSError? {
                        Logger.error("錯誤域: \(nsError.domain)")
                        Logger.error("錯誤代碼: \(nsError.code)")
                        Logger.error("錯誤資訊: \(nsError.userInfo)")
                        
                        // 特別處理iOS 18.4模擬器已知問題
                        if nsError.domain == NSURLErrorDomain && (nsError.code == -1005 || nsError.code == -1001) {
                            Logger.warning("檢測到iOS 18.4模擬器URLSession已知問題 (錯誤代碼: \(nsError.code))")
                            Logger.debug("💡 建議：在真機上測試或等待Xcode更新")
                        }
                    }
                    
                    completion(.failure(.networkError))
                    return
                }
                
                // 檢查HTTP回應狀態
                if let httpResponse = response as? HTTPURLResponse {
                    Logger.debug("Google Geocoding API HTTP狀態碼: \(httpResponse.statusCode)")
                    if httpResponse.statusCode != 200 {
                        Logger.error("Google Geocoding API HTTP錯誤: \(httpResponse.statusCode)")
                        completion(.failure(.apiError("HTTP \(httpResponse.statusCode)")))
                        return
                    }
                }
                
                guard let data = data else {
                    Logger.debug("Google Geocoding API 沒有回應資料")
                    completion(.failure(.invalidResponse))
                    return
                }
                
                // 印出原始回應資料以供調試
                if let responseString = String(data: data, encoding: .utf8) {
                    Logger.debug("Google Geocoding API 原始回應: \(responseString)")
                } else {
                    Logger.debug("Google Geocoding API 回應資料無法轉換為字串")
                }
                
                do {
                    // 先嘗試解析基本的狀態回應
                    if let jsonObject = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                        if let status = jsonObject["status"] as? String {
                            Logger.debug("Google Geocoding API 狀態: \(status)")
                            
                            // 檢查是否有錯誤訊息
                            if let errorMessage = jsonObject["error_message"] as? String {
                                Logger.error("Google Geocoding API 錯誤訊息: \(errorMessage)")
                                completion(.failure(.apiError("\(status): \(errorMessage)")))
                                return
                            }
                        }
                    }
                    
                    let response = try JSONDecoder().decode(GoogleGeocoding.Response.self, from: data)
                    Logger.success("Google Geocoding API 解析成功，狀態: \(response.status)")
                    
                    if response.status == "OK" {
                        Logger.debug("Google Geocoding API 找到 \(response.results.count) 個結果")
                        completion(.success(response.results))
                    } else {
                        Logger.error("Google Geocoding API 狀態錯誤: \(response.status)")
                        completion(.failure(.apiError(response.status)))
                    }
                } catch {
                    Logger.error("Google Geocoding 資料解析錯誤: \(error)")
                    Logger.error("解析錯誤詳細資訊: \(error.localizedDescription)")
                    if let decodingError = error as? DecodingError {
                        switch decodingError {
                        case .keyNotFound(let key, let context):
                            Logger.debug("缺少鍵值: \(key), 路徑: \(context.codingPath)")
                        case .typeMismatch(let type, let context):
                            Logger.debug("類型不匹配: \(type), 路徑: \(context.codingPath)")
                        case .valueNotFound(let type, let context):
                            Logger.debug("找不到值: \(type), 路徑: \(context.codingPath)")
                        case .dataCorrupted(let context):
                            Logger.debug("資料損壞: \(context)")
                        @unknown default:
                            Logger.error("未知解析錯誤")
                        }
                    }
                    completion(.failure(.decodingError))
                }
            }
        }.resume()
    }
}

// MARK: - API 回應模型

// MARK: - Google Geocoding API 回應模型
extension GoogleGeocoding {
    /// Google Geocoding API 回應
    struct Response: Codable {
        let results: [Result]
        let status: String
        let error_message: String?
    }

    /// Google Geocoding 結果
    struct Result: Codable, Identifiable, Hashable {
    let place_id: String
    let formatted_address: String
    let geometry: Geometry
    let address_components: [AddressComponent]
    let types: [String]
    
    var id: String { place_id }
    
    struct Geometry: Codable, Hashable {
        let location: Location
        
        struct Location: Codable, Hashable {
            let lat: Double
            let lng: Double
        }
    }
    
    struct AddressComponent: Codable, Hashable {
        let long_name: String
        let short_name: String
        let types: [String]
    }
    
    /// 轉換為SavedLocation
    func toSavedLocation() -> SavedLocation {
        // 提取城市名稱
        let cityName = extractCityName()
        // 提取國家代碼
        let countryCode = extractCountryCode()
        
        return SavedLocation(
            name: cityName,
            formattedAddress: formatted_address,
            lat: geometry.location.lat,
            lon: geometry.location.lng,
            country: countryCode
        )
    }
    
    /// 提取城市名稱
    func extractCityName() -> String {
        // 優先順序：locality -> administrative_area_level_1 -> formatted_address的第一部分
        if let area = address_components.first(where: { $0.types.contains("establishment") }) {
            return area.long_name
        }

        if let area = address_components.first(where: { $0.types.contains("sublocality_level_2") }) {
            return area.long_name
        }

        if let area = address_components.first(where: { $0.types.contains("sublocality_level_1") }) {
            return area.long_name
        }

        if let locality = address_components.first(where: { $0.types.contains("locality") }) {
            return locality.long_name
        }        

        if let area = address_components.first(where: { $0.types.contains("administrative_area_level_3") }) {
            return area.long_name
        }
        
        if let area = address_components.first(where: { $0.types.contains("administrative_area_level_2") }) {
            return area.long_name
        }

        if let area = address_components.first(where: { $0.types.contains("administrative_area_level_1") }) {
            return area.long_name
        }
        
        // 如果都找不到，使用formatted_address的第一部分
        let parts = formatted_address.components(separatedBy: ",")
        return parts.first?.trimmingCharacters(in: .whitespacesAndNewlines) ?? formatted_address
    }
    
    /// 提取國家代碼
    private func extractCountryCode() -> String {
        return address_components.first(where: { $0.types.contains("country") })?.short_name ?? ""
    }
    }
}
