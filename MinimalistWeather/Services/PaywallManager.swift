//
//  PaywallManager.swift
//  MinimalistWeather
//
//  Created by Kered on 2025/7/25.
//

import Foundation
import SwiftUI

/// Paywall 管理器，負責決定何時顯示 paywall
class PaywallManager: ObservableObject {
    // MARK: - 單例
    static let shared = PaywallManager()
    
    // MARK: - 屬性
    @Published var shouldShowPaywall = false
    
    private let iapService = IAPService.shared
    private let appSettings = AppSettings.shared
    private let userDefaults = UserDefaults.standard

    // UserDefaults Keys
    private struct Keys {
        static let lastPaywallShownDate = "lastPaywallShownDate"
    }
    
    // MARK: - 初始化
    private init() {}
    
    // MARK: - 公開方法
    
    /// 檢查是否應該顯示 paywall
    /// - Returns: 是否應該顯示 paywall
    func shouldShowPaywallOnAppLaunch() -> Bool {
        Logger.debug("🔍 PaywallManager: 開始檢查是否應該顯示 paywall")
        
        // 1. 檢查 debug 模式
        if appSettings.paywallDebugMode {
            Logger.debug("🐛 PaywallManager: Debug 模式已啟用，強制顯示 paywall")
            return true
        }
        
        // 2. 檢查是否已訂閱
        if iapService.isPro {
            Logger.success("PaywallManager: 用戶已訂閱 Pro，不顯示 paywall")
            return false
        }
        
        // 3. 檢查上次顯示時間
        let lastShownDate = getLastPaywallShownDate()
        
        if lastShownDate == nil {
            // 首次安裝或無紀錄，不顯示 paywall，但記錄當前時間
            Logger.debug("📱 PaywallManager: 首次安裝或無紀錄，不顯示 paywall，記錄當前時間")
            recordPaywallShown()
            return false
        }
        
        // 4. 檢查是否為同一天
        if let lastDate = lastShownDate, Calendar.current.isDateInToday(lastDate) {
            Logger.debug("📅 PaywallManager: 今天已顯示過 paywall，不再顯示")
            return false
        }
        
        // 5. 檢查距離上次顯示是否超過 1 天
        if let lastDate = lastShownDate {
            let daysSinceLastShown = Calendar.current.dateComponents([.day], from: lastDate, to: Date()).day ?? 0
            
            if daysSinceLastShown >= 2 {
                Logger.debug("⏰ PaywallManager: 距離上次顯示已超過 \(daysSinceLastShown) 天，顯示 paywall")
                return true
            } else {
                Logger.debug("⏰ PaywallManager: 距離上次顯示不足 1 天，不顯示 paywall")
                return false
            }
        }
        
        return false
    }
    
    /// 記錄 paywall 已顯示
    func recordPaywallShown() {
        let now = Date()
        userDefaults.set(now, forKey: Keys.lastPaywallShownDate)
        userDefaults.synchronize()
        
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        Logger.debug("📝 PaywallManager: 已記錄 paywall 顯示時間: \(formatter.string(from: now))")
    }
    
    /// 獲取上次 paywall 顯示時間
    /// - Returns: 上次顯示時間，如果沒有記錄則返回 nil
    func getLastPaywallShownDate() -> Date? {
        return userDefaults.object(forKey: Keys.lastPaywallShownDate) as? Date
    }
    
    /// 清除 paywall 顯示記錄（用於測試）
    func clearPaywallHistory() {
        userDefaults.removeObject(forKey: Keys.lastPaywallShownDate)
        userDefaults.synchronize()
        Logger.debug("🗑️ PaywallManager: 已清除 paywall 顯示記錄")
    }
    
    // MARK: - Debug 模式

    /// Debug 模式是否啟用
    var isDebugModeEnabled: Bool {
        get {
            return appSettings.paywallDebugMode
        }
        set {
            appSettings.paywallDebugMode = newValue
        }
    }

    /// 切換 debug 模式
    func toggleDebugMode() {
        appSettings.paywallDebugMode.toggle()
    }
    
    // MARK: - 便利方法
    
    /// 檢查並顯示 paywall（如果需要）
    /// - Parameter completion: 完成回調，參數表示是否顯示了 paywall
    func checkAndShowPaywallIfNeeded(completion: @escaping (Bool) -> Void) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else {
                completion(false)
                return
            }
            
            let shouldShow = self.shouldShowPaywallOnAppLaunch()
            
            if shouldShow {
                self.shouldShowPaywall = true
                self.recordPaywallShown()
                completion(true)
            } else {
                completion(false)
            }
        }
    }
    
    /// 手動顯示 paywall
    func showPaywall() {
        DispatchQueue.main.async { [weak self] in
            self?.shouldShowPaywall = true
            self?.recordPaywallShown()
        }
    }
    
    /// 隱藏 paywall
    func hidePaywall() {
        DispatchQueue.main.async { [weak self] in
            self?.shouldShowPaywall = false
        }
    }
    
    // MARK: - 調試信息
    
    /// 獲取當前狀態信息（用於調試）
    func getDebugInfo() -> String {
        let lastShownDate = getLastPaywallShownDate()
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        
        var info = """
        PaywallManager Debug Info:
        - 是否已訂閱: \(iapService.isPro)
        - Debug 模式: \(appSettings.paywallDebugMode)
        - 上次顯示時間: \(lastShownDate != nil ? formatter.string(from: lastShownDate!) : "無記錄")
        - 應該顯示 paywall: \(shouldShowPaywallOnAppLaunch())
        """
        
        if let lastDate = lastShownDate {
            let daysSince = Calendar.current.dateComponents([.day], from: lastDate, to: Date()).day ?? 0
            info += "\n- 距離上次顯示: \(daysSince) 天"
            info += "\n- 是否為今天: \(Calendar.current.isDateInToday(lastDate))"
        }
        
        return info
    }
}
