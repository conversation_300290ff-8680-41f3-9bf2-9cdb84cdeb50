//
//  WidgetFontLoader.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import Foundation
import UIKit
import CoreGraphics
import CoreText

/// Widget 專用的自定義字體加載工具類
struct WidgetFontLoader {
    // 字體名稱常量
    static let customFontName = "Icon"
    
    // 加載字體並返回是否成功
    static func loadCustomFonts() -> Bool {
        // 檢查字體是否已經註冊
        if UIFont.fontNames(forFamilyName: customFontName).count > 0 {
            Logger.debug("🔍 Widget: 字體已註冊：\(customFontName)")
            return true
        }
        
        // 嘗試從 Widget Bundle 中找到字體文件
        guard let fontURL = getWidgetFontURL() else {
            Logger.error("Widget: 找不到字體文件: \(customFontName).ttf")
            return false
        }
        
        // 使用新API註冊字體
        var error: Unmanaged<CFError>?
        
        // 使用CTFontManagerRegisterFontsForURL替代棄用的API
        if !CTFontManagerRegisterFontsForURL(fontURL as CFURL, .process, &error) {
            Logger.error("Widget: 註冊字體錯誤: \(error.debugDescription)")
            return false
        }
        
        Logger.success("Widget: 成功註冊字體：\(customFontName)")
        return true
    }
    
    // 備用方法，使用Data創建字體描述符（iOS 18及以上推薦）
    static func loadCustomFontsUsingData() -> Bool {
        // 檢查字體是否已經註冊
        if UIFont.fontNames(forFamilyName: customFontName).count > 0 {
            Logger.debug("🔍 Widget: 字體已註冊：\(customFontName)")
            return true
        }
        
        // 嘗試從 Widget Bundle 中找到字體文件
        guard let fontURL = getWidgetFontURL() else {
            Logger.error("Widget: 找不到字體文件: \(customFontName).ttf")
            return false
        }
        
        guard let fontData = try? Data(contentsOf: fontURL) else {
            Logger.error("Widget: 無法讀取字體文件")
            return false
        }
        
        // 使用CTFontManagerCreateFontDescriptorsFromData創建字體描述符
        let descriptors = CTFontManagerCreateFontDescriptorsFromData(fontData as CFData)
        
        // 檢查描述符是否有效
        if CFArrayGetCount(descriptors) == 0 {
            Logger.error("Widget: 無法創建有效的字體描述符或描述符為空")
            return false
        }
        
        // 註冊所有字體描述符 - 不檢查返回值，而是通過加載後的字體是否存在來驗證
        CTFontManagerRegisterFontDescriptors(descriptors, .process, true, nil)
        
        // 驗證字體是否成功加載
        if UIFont.fontNames(forFamilyName: customFontName).count > 0 {
            Logger.success("Widget: 成功註冊字體（使用Data方法）：\(customFontName)")
            return true
        } else {
            Logger.error("Widget: 註冊字體描述符失敗：無法找到字體 \(customFontName)")
            return false
        }
    }
    
    // 獲取 Widget Bundle 中的字體文件 URL
    private static func getWidgetFontURL() -> URL? {
        // 首先嘗試從當前 Bundle 中查找
        if let url = Bundle.main.url(forResource: "Icon", withExtension: "ttf") {
            return url
        }
        
        // 如果在主 Bundle 中找不到，嘗試在 Resources 目錄中查找
        if let resourcesPath = Bundle.main.path(forResource: "Resources", ofType: nil) {
            let fontPath = "\(resourcesPath)/Icon.ttf"
            if FileManager.default.fileExists(atPath: fontPath) {
                return URL(fileURLWithPath: fontPath)
            }
        }
        
        // 最後嘗試直接構建路徑
        let bundlePath = Bundle.main.bundlePath
        let fontPath = "\(bundlePath)/Resources/Icon.ttf"
        if FileManager.default.fileExists(atPath: fontPath) {
            return URL(fileURLWithPath: fontPath)
        }
        
        return nil
    }
    
    // 判斷自定義字體是否可用
    static func isCustomFontAvailable() -> Bool {
        return UIFont.fontNames(forFamilyName: customFontName).count > 0
    }
    
    // 列出所有可用字體（用於調試）
    static func listAllAvailableFonts() {
        Logger.debug("🔍 Widget: 列出所有可用字體")
        for family in UIFont.familyNames.sorted() {
            Logger.debug("字體家族: \(family)")
            for name in UIFont.fontNames(forFamilyName: family).sorted() {
                Logger.debug("   字體: \(name)")
            }
        }
    }
}
